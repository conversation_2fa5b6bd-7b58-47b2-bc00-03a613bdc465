#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快捷一键功能对话框模块
提供快捷一键功能的配置和进度显示对话框
"""

import os
import sys
import json
import time
import math
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, QFrame, 
    QPlainTextEdit, QSpinBox, QScrollArea, QWidget, QSizePolicy, QGridLayout,
    QAbstractSpinBox
)
from PySide6.QtCore import Qt, Signal, QTimer, QPropertyAnimation, QEasingCurve, Property, QParallelAnimationGroup, QRect, QPointF, QPoint
from PySide6.QtGui import QTextCursor, QColor, QPainter, QPen, QBrush, QPixmap, QFont, QPainterPath, QLinearGradient
from enum import Enum, auto

from widgets.dialog import StyledDialog
from widgets.styled_widgets import StyledProgressBar, StyledSwitch
from theme import Theme
from utils import get_app_data_dir
from logger import info, warning, error, debug

from widgets.quick_function_worker import QuickFunctionWorker

class FunctionStatus(Enum):
    """功能执行状态枚举"""
    WAITING = auto()    # 等待执行
    RUNNING = auto()    # 正在执行
    SUCCESS = auto()    # 执行成功
    FAILED = auto()     # 执行失败


class FunctionCard(QFrame):
    """功能卡片组件
    
    用于显示功能执行状态的卡片，包含动画效果
    """
    
    def __init__(self, emoji, title, parent=None):
        """初始化功能卡片
        
        Args:
            emoji: 功能Emoji图标
            title: 功能标题
            parent: 父窗口
        """
        super().__init__(parent)
        self.emoji = emoji
        self.title = title
        self.status = FunctionStatus.WAITING
        self.message = ""  # 额外消息，如注册成功的邮箱等
        
        # 动画相关属性
        self._progress = 0.0  # 进度动画值 (0.0 - 1.0)
        self._opacity = 0.0   # 卡片整体不透明度
        self._glow_position = -0.5  # 流光位置 (-0.5 to 1.5)
        self._draw_progress = 0.0 # 图标绘制进度 (0.0 - 1.0)
        
        # 新增入场动画属性
        self._offset_x = 30  # 初始X轴偏移量
        self._offset_y = 10  # 初始Y轴偏移量
        self._scale = 0.95   # 初始缩放比例
        
        # 设置固定大小和样式
        self.setFixedHeight(55)  # 降低高度
        self.setMinimumWidth(180)
        self.setStyleSheet(f"""
            FunctionCard {{
                background-color: {Theme.CARD_LEVEL_1};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                border: 1px solid {Theme.BORDER};
            }}
        """)
        
        # 创建不透明度动画
        self._fade_animation = QPropertyAnimation(self, b"opacity")
        self._fade_animation.setDuration(550)  # 增加持续时间，让淡入效果更慢
        self._fade_animation.setEasingCurve(QEasingCurve.Type.OutCubic)
        self._fade_animation.setStartValue(0.0)
        self._fade_animation.setEndValue(1.0)
        
        # 创建进度动画
        self._progress_animation = QPropertyAnimation(self, b"progress")
        self._progress_animation.setDuration(800)
        self._progress_animation.setEasingCurve(QEasingCurve.Type.InOutSine)  # 改为更平滑的曲线
        
        # 创建流光动画
        self._glow_animation = QPropertyAnimation(self, b"glowPosition")
        self._glow_animation.setDuration(1800)
        self._glow_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
        self._glow_animation.setStartValue(-0.5) # 流光从左侧外部开始
        self._glow_animation.setEndValue(1.5)   # 流光到右侧外部结束
        self._glow_animation.setLoopCount(-1)  # 无限循环
        
        # 创建图标绘制动画
        self._draw_animation = QPropertyAnimation(self, b"drawProgress")
        self._draw_animation.setDuration(500) # 绘制动画时长
        self._draw_animation.setEasingCurve(QEasingCurve.Type.InOutQuad) # 缓动曲线
        
        # 创建X轴偏移动画
        self._offset_x_animation = QPropertyAnimation(self, b"offsetX")
        self._offset_x_animation.setDuration(800)  # 增加持续时间，让动画更慢
        self._offset_x_animation.setEasingCurve(QEasingCurve.Type.OutBack)
        self._offset_x_animation.setStartValue(self._offset_x)
        self._offset_x_animation.setEndValue(0)
        
        # 创建Y轴偏移动画
        self._offset_y_animation = QPropertyAnimation(self, b"offsetY")
        self._offset_y_animation.setDuration(800)  # 增加持续时间，让动画更慢
        self._offset_y_animation.setEasingCurve(QEasingCurve.Type.OutBack)
        self._offset_y_animation.setStartValue(self._offset_y)
        self._offset_y_animation.setEndValue(0)
        
        # 创建缩放动画
        self._scale_animation = QPropertyAnimation(self, b"scale")
        self._scale_animation.setDuration(800)  # 增加持续时间，让动画更慢
        self._scale_animation.setEasingCurve(QEasingCurve.Type.OutBack)
        self._scale_animation.setStartValue(self._scale)
        self._scale_animation.setEndValue(1.0)
        
        # 创建入场动画组
        self._entrance_animation_group = QParallelAnimationGroup()
        self._entrance_animation_group.addAnimation(self._fade_animation)
        self._entrance_animation_group.addAnimation(self._offset_x_animation)
        self._entrance_animation_group.addAnimation(self._offset_y_animation)
        self._entrance_animation_group.addAnimation(self._scale_animation)
        
        # 不需要鼠标跟踪
        # self.setMouseTracking(True)
        
        # 初始化时完全不可见
        self.set_opacity(0.0)
        self.setVisible(False)
    
    def _start_fade_in(self):
        """开始淡入动画"""
        # 使用组合动画代替单独的淡入动画
        self._entrance_animation_group.start()
    
    def start_entrance_animation(self, delay=0):
        """开始入场动画，支持延迟
        
        Args:
            delay: 延迟启动时间(毫秒)
        """
        if delay > 0:
            QTimer.singleShot(delay, self._start_fade_in)
        else:
            self._start_fade_in()
    
    def set_status(self, status, message=""):
        """设置功能执行状态
        
        Args:
            status: 功能状态 (FunctionStatus枚举)
            message: 附加消息
        """
        if self.status == status:
            return
            
        # 保存旧状态用于动画转换
        old_status = self.status
        
        # 更新状态和消息
        self.status = status
        self.message = message
        
        # 停止可能正在运行的动画
        self._progress_animation.stop()
        self._glow_animation.stop()
        self._draw_animation.stop() # 停止绘制动画
        
        # 重置绘制进度，除非新状态是成功或失败（这些状态会自己启动绘制）
        if status != FunctionStatus.SUCCESS and status != FunctionStatus.FAILED:
             self._draw_progress = 0.0

        # 根据状态变化设置动画
        if status == FunctionStatus.RUNNING:
            # 开始运行动画
            self._progress_animation.setStartValue(0.0)
            self._progress_animation.setEndValue(0.9)  # 不到100%，表示仍在进行中
            self._progress_animation.setDuration(800)
            self._progress_animation.start()
            
            # 启动流光动画
            self._glow_animation.start()
        elif status == FunctionStatus.SUCCESS:
            # 完成动画 - 先进行进度填满，再启动图标绘制
            current_progress = self._progress
            
            # 断开可能已有的连接
            try:
                self._progress_animation.finished.disconnect()
            except:
                pass
                
            # 延长进度完成动画时间，使填充更丝滑
            self._progress_animation.setStartValue(current_progress)
            self._progress_animation.setEndValue(1.0)
            self._progress_animation.setDuration(650)  # 延长动画时间
            self._progress_animation.setEasingCurve(QEasingCurve.Type.InOutSine)  # 使用更丝滑的曲线
            
            # 进度动画完成后再启动图标绘制
            self._draw_progress = 0.0
            self._draw_animation.setStartValue(0.0)
            self._draw_animation.setEndValue(1.0)
            
            # 连接进度动画完成信号，确保先填满进度条，再绘制图标
            self._progress_animation.finished.connect(self._start_draw_animation)
            
            # 启动进度动画
            self._progress_animation.start()
        elif status == FunctionStatus.FAILED:
            # 失败动画 - 采用与成功状态相同的流程，先完成进度
            
            # 断开可能已有的连接
            try:
                self._progress_animation.finished.disconnect()
            except:
                pass
            
            # 对于失败状态，先完成当前进度到90%，而不是填满
            current_progress = self._progress
            target_progress = min(0.9, current_progress)  # 保持在90%以内
            
            if current_progress < target_progress:
                # 如果当前进度小于目标，先完成进度
                self._progress_animation.setStartValue(current_progress)
                self._progress_animation.setEndValue(target_progress)
                self._progress_animation.setDuration(350)
                self._progress_animation.setEasingCurve(QEasingCurve.Type.InOutSine)
                
                # 进度结束后启动图标动画
                self._progress_animation.finished.connect(self._start_draw_animation)
                self._progress_animation.start()
            else:
                # 否则直接启动图标动画
                self._draw_progress = 0.0
                self._draw_animation.setStartValue(0.0)
                self._draw_animation.setEndValue(1.0)
                self._draw_animation.start()
        
        # 强制重绘
        self.update()
    
    def get_progress(self):
        """获取进度值"""
        return self._progress
    
    def set_progress(self, value):
        """设置进度值"""
        self._progress = value
        self.update()  # 更新绘制
    
    def get_opacity(self):
        """获取不透明度值"""
        return self._opacity
    
    def set_opacity(self, value):
        """设置不透明度值"""
        self._opacity = value
        self.update()  # 更新绘制
    
    def get_drawProgress(self):
        return self._draw_progress
        
    def set_drawProgress(self, value):
        self._draw_progress = value
        self.update() # 更新绘制
    
    def get_glowPosition(self):
        """获取流光位置值"""
        return self._glow_position
    
    def set_glowPosition(self, value):
        """设置流光位置值"""
        self._glow_position = value
        if self.status == FunctionStatus.RUNNING:
            self.update()  # 仅在运行时更新
    
    # 新增属性的getter和setter
    def get_offsetX(self):
        """获取X轴偏移值"""
        return self._offset_x
    
    def set_offsetX(self, value):
        """设置X轴偏移值"""
        self._offset_x = value
        self.update()
    
    def get_offsetY(self):
        """获取Y轴偏移值"""
        return self._offset_y
    
    def set_offsetY(self, value):
        """设置Y轴偏移值"""
        self._offset_y = value
        self.update()
    
    def get_scale(self):
        """获取缩放值"""
        return self._scale
    
    def set_scale(self, value):
        """设置缩放值"""
        self._scale = value
        self.update()
    
    # 定义属性
    progress = Property(float, get_progress, set_progress)
    opacity = Property(float, get_opacity, set_opacity)
    glowPosition = Property(float, get_glowPosition, set_glowPosition)
    drawProgress = Property(float, get_drawProgress, set_drawProgress)
    offsetX = Property(float, get_offsetX, set_offsetX)  # 新增X轴偏移属性
    offsetY = Property(float, get_offsetY, set_offsetY)  # 新增Y轴偏移属性
    scale = Property(float, get_scale, set_scale)        # 新增缩放属性
    
    # 删除鼠标悬浮事件处理
    
    def paintEvent(self, event):
        """绘制事件处理
        
        绘制卡片内容，包括背景、图标、标题和状态指示器
        """
        painter = QPainter(self)
        
        try:
            painter.setRenderHint(QPainter.RenderHint.Antialiasing)
            
            # 保存绘图状态用于变换
            painter.save()
            
            # 计算尺寸
            width = self.width()
            height = self.height()
            
            # 设置透明度
            painter.setOpacity(self._opacity)
            
            # 应用偏移和缩放变换
            if self._offset_x != 0 or self._offset_y != 0 or self._scale != 1.0:
                # 计算中心点
                center_x = width / 2
                center_y = height / 2
                
                # 移动到中心点
                painter.translate(center_x, center_y)
                
                # 应用缩放
                painter.scale(self._scale, self._scale)
                
                # 应用偏移（需要考虑缩放效果）
                painter.translate(self._offset_x / self._scale, self._offset_y / self._scale)
                
                # 移回原点
                painter.translate(-center_x, -center_y)
            
            # 绘制背景
            painter.setPen(Qt.PenStyle.NoPen)
            
            # 基础背景色，统一使用基本色
            bg_color = QColor(Theme.CARD_LEVEL_1)
            painter.setBrush(QBrush(bg_color))
            painter.drawRoundedRect(0, 0, width, height, 10, 10)
            
            # 获取状态对应的颜色
            status_color = self._get_status_color()
            
            # 如果有进度，绘制进度背景
            if self._progress > 0:
                progress_width = int(width * self._progress)
                
                # 创建平滑的进度路径
                progress_path = QPainterPath()
                
                # 更精确计算进度条宽度，使用浮点数避免取整造成的不平滑
                progress_width_float = width * self._progress
                
                # 添加圆角矩形
                progress_path.addRoundedRect(0, 0, progress_width_float, height, 10, 10)
                
                # 对于超出的部分，使用矩形裁剪，但保持边缘平滑
                if progress_width < width - 5:  # 留有余量避免接近100%时的剪裁
                    rect_path = QPainterPath()
                    rect_path.addRect(progress_width_float - 10, 0, 10, height)
                    progress_path = progress_path.subtracted(rect_path)
                
                # 使用半透明的状态颜色绘制基础进度背景
                progress_color = QColor(status_color)
                progress_color.setAlphaF(0.2) # 固定半透明
                painter.setBrush(progress_color)
                painter.drawPath(progress_path)
                
                # 如果是运行状态，绘制流光
                if self.status == FunctionStatus.RUNNING and progress_width > 0:
                    painter.save()
                    painter.setClipPath(progress_path) # 裁剪流光到进度区域
                    
                    glow_width_pixels = width * 0.25 # 流光宽度调整
                    glow_center_x = width * self._glow_position
                    glow_start_x = glow_center_x - glow_width_pixels / 2
                    glow_end_x = glow_center_x + glow_width_pixels / 2
                    
                    gradient = QLinearGradient(glow_start_x, 0, glow_end_x, 0)
                    
                    # 使用状态色的亮版本作为流光色
                    glow_base_color = QColor(status_color)
                    h, s, v, a = glow_base_color.getHsvF()
                    highlight_v = min(1.0, v + 0.3) # 提高亮度
                    highlight_s = max(0.0, s - 0.2) # 降低饱和度
                    
                    center_color = QColor.fromHsvF(h, highlight_s, highlight_v)
                    center_color.setAlphaF(0.45) # 流光中心不透明度调整
                    
                    edge_color = QColor(center_color)
                    edge_color.setAlphaF(0.0) # 边缘完全透明
                    
                    gradient.setColorAt(0.0, edge_color)
                    gradient.setColorAt(0.5, center_color)
                    gradient.setColorAt(1.0, edge_color)
                    
                    painter.setBrush(QBrush(gradient))
                    painter.setPen(Qt.PenStyle.NoPen)
                    painter.drawRect(0, 0, width, height)
                    
                    painter.restore()
            
            # 绘制左侧状态指示条
            painter.setPen(Qt.PenStyle.NoPen)
            
            # 状态色，运行时更亮
            accent_color = QColor(status_color)
            if self.status == FunctionStatus.RUNNING:
                # 增强色彩饱和度
                h, s, v, a = accent_color.getHsvF()
                s = min(1.0, s * 1.2)  # 增加饱和度
                v = min(1.0, v * 1.2)  # 增加亮度
                accent_color.setHsvF(h, s, v, a)
            
            painter.setBrush(accent_color)
            painter.drawRoundedRect(0, 0, 4, height, 2, 2)
            
            # 绘制Emoji图标
            emoji_font = QFont()
            emoji_font.setPointSize(16)
            painter.setFont(emoji_font)
            painter.setPen(QColor(Theme.TEXT_PRIMARY))
            painter.drawText(QRect(15, 0, 30, height), Qt.AlignmentFlag.AlignVCenter, self.emoji)
            
            # 绘制标题文字
            title_font = QFont()
            title_font.setPointSize(9)
            title_font.setBold(True)
            painter.setFont(title_font)
            painter.setPen(QColor(Theme.TEXT_PRIMARY))
            
            # 计算文本区域，留出状态图标的空间
            text_rect = QRect(50, 0, width - 80, height)
            painter.drawText(text_rect, Qt.AlignmentFlag.AlignVCenter, self.title)
            
            # 根据状态绘制右侧状态图标或文字
            self._draw_status_indicator(painter, width, height)
            
            # 如果有附加消息且状态为成功，绘制在标题下方
            if self.message and self.status == FunctionStatus.SUCCESS:
                message_font = QFont()
                message_font.setPointSize(8)
                painter.setFont(message_font)
                painter.setPen(QColor(Theme.TEXT_SECONDARY))
                # 调整 Y 坐标，使其更靠下
                message_rect = QRect(50, height // 2 + 6, width - 80, height // 2 - 8)
                # 省略长消息
                elided_message = painter.fontMetrics().elidedText(
                    self.message, Qt.TextElideMode.ElideMiddle, message_rect.width()
                )
                # 使用 AlignVCenter 使其在调整后的矩形内垂直居中
                painter.drawText(message_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter, elided_message)
            
            # 恢复绘图状态
            painter.restore()
        finally:
            # 确保在任何情况下都结束painter
            painter.end()
    
    def _get_point_on_line(self, start_point, end_point, ratio):
        """获取线段上特定比例的点"""
        return start_point + (end_point - start_point) * ratio

    def _draw_status_indicator(self, painter, width, height):
        """绘制状态指示器，包含绘制动画"""
        # 状态指示器的位置在右侧，缩小大小从24x24到20x20
        status_rect = QRect(width - 33, height // 2 - 10, 20, 20)
        center_x = status_rect.center().x()
        center_y = status_rect.center().y()
        radius = status_rect.width() / 2

        if self.status == FunctionStatus.WAITING:
            # 等待状态 - 绘制一个灰色圆圈
            painter.setPen(QPen(QColor(Theme.TEXT_SECONDARY), 1.5))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            painter.drawEllipse(status_rect)
        elif self.status == FunctionStatus.RUNNING:
            # 运行状态 - 绘制旋转的半圆弧，线宽从2增加到3
            painter.setPen(QPen(QColor(Theme.ACCENT), 3))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            angle = int(self._glow_position * 720) % 360
            painter.drawArc(status_rect, angle * 16, 270 * 16)
        elif self.status == FunctionStatus.SUCCESS:
            # 成功状态 - 绘制标准对勾动画
            painter.setPen(QPen(QColor(Theme.SUCCESS), 3, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap, Qt.PenJoinStyle.RoundJoin))
            painter.setBrush(Qt.BrushStyle.NoBrush)
            
            # 标准对勾设计 - 使用相对于矩形中心的计算，创建标准勾形
            # 使用中心点为基准，按照常规对勾的比例进行计算
            size = min(status_rect.width(), status_rect.height()) * 0.6  # 对勾大小为矩形的60%
            
            # 对勾三个点的位置 - 相对中心的偏移量
            # 以下偏移量经过精确计算，创建标准的对勾形状
            p1 = QPointF(center_x - size*0.35, center_y)              # 左侧点
            p2 = QPointF(center_x - size*0.05, center_y + size*0.35)  # 底部点，稍微偏左
            p3 = QPointF(center_x + size*0.45, center_y - size*0.45)  # 右上点
            
            # 计算线段长度
            line1_vec = p2 - p1
            len1 = math.sqrt(line1_vec.x()**2 + line1_vec.y()**2)
            line2_vec = p3 - p2
            len2 = math.sqrt(line2_vec.x()**2 + line2_vec.y()**2)
            total_len = len1 + len2

            if total_len == 0: return

            current_draw_len = self._draw_progress * total_len
            
            # 绘制第一段
            if current_draw_len > 0:
                draw_len1 = min(current_draw_len, len1)
                ratio1 = draw_len1 / len1 if len1 > 0 else 1.0
                end_p1 = self._get_point_on_line(p1, p2, ratio1)
                painter.drawLine(p1, end_p1)
            
            # 绘制第二段 (如果需要)
            if current_draw_len > len1:
                draw_len2 = current_draw_len - len1
                ratio2 = draw_len2 / len2 if len2 > 0 else 1.0
                end_p2 = self._get_point_on_line(p2, p3, ratio2)
                painter.drawLine(p2, end_p2)

        elif self.status == FunctionStatus.FAILED:
            # 失败状态 - 绘制叉号动画，线宽从2增加到3
            painter.setPen(QPen(QColor(Theme.ERROR), 3, Qt.PenStyle.SolidLine, Qt.PenCapStyle.RoundCap, Qt.PenJoinStyle.RoundJoin))
            painter.setBrush(Qt.BrushStyle.NoBrush)

            # 叉号路径点 (基于status_rect调整), 显式转换为QPointF
            p1 = QPointF(status_rect.topLeft()) + QPointF(6, 6)
            p2 = QPointF(status_rect.bottomRight()) - QPointF(6, 6)
            p3 = QPointF(status_rect.topRight()) - QPointF(6, -6)
            p4 = QPointF(status_rect.bottomLeft()) + QPointF(6, -6)
            
            # 计算线段长度
            line1_vec = p2 - p1
            len1 = math.sqrt(line1_vec.x()**2 + line1_vec.y()**2)
            line2_vec = p4 - p3
            len2 = math.sqrt(line2_vec.x()**2 + line2_vec.y()**2)
            total_len = len1 + len2

            if total_len == 0: return

            current_draw_len = self._draw_progress * total_len

            # 绘制第一段 (左上 -> 右下)
            if current_draw_len > 0:
                draw_len1 = min(current_draw_len, len1)
                ratio1 = draw_len1 / len1 if len1 > 0 else 1.0
                end_p1 = self._get_point_on_line(p1, p2, ratio1)
                painter.drawLine(p1, end_p1)

            # 绘制第二段 (右上 -> 左下)
            if current_draw_len > len1:
                draw_len2 = current_draw_len - len1
                ratio2 = draw_len2 / len2 if len2 > 0 else 1.0
                end_p2 = self._get_point_on_line(p3, p4, ratio2)
                painter.drawLine(p3, end_p2)
    
    def _get_status_color(self):
        """获取状态对应的颜色
        
        Returns:
            str: 颜色代码
        """
        if self.status == FunctionStatus.WAITING:
            return Theme.TEXT_SECONDARY
        elif self.status == FunctionStatus.RUNNING:
            return Theme.ACCENT
        elif self.status == FunctionStatus.SUCCESS:
            return Theme.SUCCESS
        elif self.status == FunctionStatus.FAILED:
            return Theme.ERROR
        return Theme.TEXT_SECONDARY

    def _start_draw_animation(self):
        """启动绘制动画，用于在进度动画完成后调用"""
        # 启动图标绘制动画
        self._draw_animation.start()
        
        # 强制重绘
        self.update()


class QuickFunctionConfigDialog(StyledDialog):
    """快捷一键功能配置对话框
    
    允许用户配置快捷一键功能的各项设置，包括需要执行的功能和延迟时间
    """
    
    def __init__(self, parent=None):
        """初始化对话框
        
        Args:
            parent: 父窗口
        """
        super().__init__(parent, width=750)
        self.init_ui()
        self.load_config()
        
    def init_ui(self):
        """初始化用户界面"""
        # 移除标题
        # title_label = QLabel("快捷一键配置") ...
        
        # --- 顶部紧凑配置区域 ---
        top_config_layout = QHBoxLayout()
        top_config_layout.setSpacing(20)

        # 延迟设置
        delay_label = QLabel("间隔(秒):")
        delay_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY};")
        self.delay_spinner = QSpinBox()
        self.delay_spinner.setMinimum(0)
        self.delay_spinner.setMaximum(10)
        self.delay_spinner.setValue(2)
        self.delay_spinner.setButtonSymbols(QAbstractSpinBox.ButtonSymbols.NoButtons)
        self.delay_spinner.setStyleSheet(f"""
            QSpinBox {{
                background-color: {Theme.CARD_LEVEL_1};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 5px;
                color: {Theme.TEXT_PRIMARY};
                min-width: 40px;
                qproperty-alignment: AlignCenter; /* 文本居中 */
            }}
        """)
        top_config_layout.addWidget(delay_label)
        top_config_layout.addWidget(self.delay_spinner)
        top_config_layout.addStretch(1) # 添加伸缩

        # 失败时停止选项
        stop_on_failure_label = QLabel("失败时停止:")
        stop_on_failure_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY};")
        self.stop_on_failure_switch = StyledSwitch()
        self.stop_on_failure_switch.setChecked(True)  # 默认开启
        stop_on_failure_desc = QLabel("(失败则停止后续)") # 简化描述
        stop_on_failure_desc.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
        """)
        top_config_layout.addWidget(stop_on_failure_label)
        top_config_layout.addWidget(stop_on_failure_desc)
        top_config_layout.addWidget(self.stop_on_failure_switch)

        self.addLayout(top_config_layout)
        
        # --- 功能开关网格区域 ---
        functions_grid_layout = QGridLayout()
        functions_grid_layout.setSpacing(10) # 设置网格间距
        functions_grid_layout.setContentsMargins(0, 15, 0, 15) # 上下边距

        self.function_switches = {}
        
        # 定义功能、Emoji、标题 和工具提示 (使用单一Emoji)
        functions = [
            ("save_current_account", "💾", "保存账户", "保存当前登录账户"),
            ("reset_machine_id", "🔑", "重置机器码", "重置系统和Cursor的机器标识"),
            ("backup_cursor_settings", "🔧", "备份设置", "备份Cursor设置和配置文件"),
            ("backup_cursor_workspace", "📑", "备份会话", "备份Cursor工作区和历史记录"),
            ("initialize_cursor", "✨", "初始化", "重置Cursor为初始状态"),
            ("auto_register", "🤖", "自动注册", "自动注册新的Cursor账号"),
            ("restore_cursor_settings", "🛠️", "恢复设置", "从备份恢复Cursor设置"),
            ("restore_cursor_workspace", "📖", "恢复会话", "从备份恢复Cursor工作区和历史记录")
        ]

        # 定义网格布局 (2行4列)
        rows, cols = 2, 4
        for idx, (func_id, emoji, title, tooltip) in enumerate(functions):
            row = idx // cols
            col = idx % cols
            toggle_widget = self._create_function_toggle(func_id, emoji, title, tooltip)
            functions_grid_layout.addWidget(toggle_widget, row, col)

        self.addLayout(functions_grid_layout)
        
        # 添加确认和取消按钮 (复用之前的 addButtons)
        self.confirm_btn = self.addButtons("开始执行", "取消")
        self.confirm_btn.clicked.connect(self.on_confirm)
        
        # 连接配置变化信号 (确保在所有控件创建后调用)
        self.connect_signals()
        
        # 将初始焦点设置给"开始执行"按钮，避免spinbox默认获取焦点
        self.confirm_btn.setFocus()
    
    def _create_function_toggle(self, func_id, emoji, title, tooltip):
        """创建卡片式功能切换控件 (水平布局: [Emoji+Title] + Switch)
        
        Args:
            func_id: 功能ID
            emoji: 功能Emoji
            title: 功能标题
            tooltip: 功能工具提示
            
        Returns:
            QFrame: 包含Emoji、标题和开关的卡片控件
        """
        # 创建可点击的自定义卡片
        class ClickableCard(QFrame):
            def __init__(self, parent=None):
                super().__init__(parent)
                # 设置鼠标指针为手型，提示用户可点击
                self.setCursor(Qt.CursorShape.PointingHandCursor)
                self.switch = None  # 将在外部设置
                
            # 恢复mouseReleaseEvent方法，但优化逻辑避免与开关点击冲突
            def mouseReleaseEvent(self, event):
                # 判断点击是否在开关控件的区域内
                if self.switch:
                    # 将事件坐标从卡片坐标系转换到开关坐标系
                    switch_pos = self.switch.mapFrom(self, event.pos())
                    
                    # 检查点击是否在开关控件区域内
                    if not self.switch.rect().contains(switch_pos):
                        # 如果点击不在开关区域，则切换开关状态
                        self.switch.setChecked(not self.switch.isChecked())
                
                # 调用父类方法
                super().mouseReleaseEvent(event)

        # 使用可点击卡片替代普通QFrame
        card = ClickableCard()
        card.setObjectName(f"funcCard_{func_id}")
        # 设置卡片样式 - 移除交互相关的样式，保持简洁的基础样式
        card.setStyleSheet(f"""
            QFrame#funcCard_{func_id} {{
                background-color: rgba(255, 255, 255, 0.03);
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px;
            }}
        """)
        
        main_layout = QHBoxLayout(card) # 卡片内部使用水平布局
        main_layout.setContentsMargins(5, 0, 5, 0) # 左右留边距，上下由padding控制
        main_layout.setSpacing(10)

        # --- 左侧部分 (Emoji + Title in HBox) ---
        left_widget = QWidget()
        left_layout = QHBoxLayout(left_widget) # 使用水平布局
        left_layout.setContentsMargins(0, 0, 0, 0)
        left_layout.setSpacing(5) # Emoji和标题间距

        emoji_label = QLabel(emoji)
        emoji_label.setStyleSheet("background: transparent; font-size: 20px;")
        # emoji_label.setAlignment(Qt.AlignmentFlag.AlignCenter) # 水平布局不需要中心对齐

        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            background: transparent;
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_SMALL};
            /* 垂直居中对齐Emoji */
            padding-top: 2px; 
        """)
        # title_label.setAlignment(Qt.AlignmentFlag.AlignCenter) # 水平布局不需要中心对齐

        left_layout.addWidget(emoji_label)
        left_layout.addWidget(title_label)
        # left_layout.addStretch() # 不需要伸缩，让它们紧挨着

        # --- 右侧部分 (Switch) ---
        switch = StyledSwitch()
        switch.setToolTip(tooltip)
        
        # --- 组合布局 ---
        main_layout.addWidget(left_widget) # 添加包含图标和文字的左侧部件
        main_layout.addStretch() # 将开关推到右侧
        main_layout.addWidget(switch, alignment=Qt.AlignmentFlag.AlignVCenter) # 开关垂直居中

        # 保存开关引用到卡片和全局字典
        card.switch = switch
        self.function_switches[func_id] = switch
        
        # 设置整体控件的工具提示
        card.setToolTip(tooltip)
        card.setMinimumHeight(50) # 设置一个合适的最小高度
        
        return card
        
    def load_config(self):
        """加载配置"""
        try:
            # 获取配置文件路径
            config_dir = os.path.join(get_app_data_dir(), "config")
            os.makedirs(config_dir, exist_ok=True)
            config_file = os.path.join(config_dir, "quick_function.json")
            
            # 默认配置
            default_config = {
                "delay": 3, # 修改默认延迟为 3
                "stop_on_failure": True,  # 默认开启失败时停止
                "functions": {
                    "save_current_account": False, # 默认关闭
                    "reset_machine_id": True,    # 默认开启
                    "backup_cursor_settings": True,  # 默认开启
                    "backup_cursor_workspace": True, # 默认开启
                    "initialize_cursor": True,     # 默认开启
                    "auto_register": True,         # 默认开启
                    "restore_cursor_settings": True, # 默认开启
                    "restore_cursor_workspace": True # 默认开启
                }
            }
            
            # 如果配置文件存在，读取配置
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                
                # 合并配置，确保所有字段都存在
                if "functions" not in config:
                    config["functions"] = default_config["functions"]
                else:
                    for func_id in default_config["functions"]:
                        if func_id not in config["functions"]:
                            config["functions"][func_id] = default_config["functions"][func_id]
                
                if "delay" not in config:
                    config["delay"] = default_config["delay"]
                
                # 处理新增的stop_on_failure选项
                if "stop_on_failure" not in config:
                    config["stop_on_failure"] = default_config["stop_on_failure"]
            else:
                # 使用默认配置
                config = default_config
                
                # 保存默认配置
                with open(config_file, 'w', encoding='utf-8') as f:
                    json.dump(config, f, ensure_ascii=False, indent=2)
            
            # 应用配置到界面
            self.delay_spinner.setValue(config["delay"])
            self.stop_on_failure_switch.setChecked(config["stop_on_failure"])
            
            for func_id, enabled in config["functions"].items():
                if func_id in self.function_switches:
                    self.function_switches[func_id].setChecked(enabled)
            
            return config
        except Exception as e:
            error(f"加载快捷一键配置失败: {str(e)}")
            # 返回默认配置
            return {
                "delay": 5, # 修改默认延迟为 5
                "stop_on_failure": True,  # 默认开启失败时停止
                "functions": {
                    "save_current_account": False, # 默认关闭
                    "reset_machine_id": True,    # 默认开启
                    "backup_cursor_settings": True,  # 默认开启
                    "backup_cursor_workspace": True, # 默认开启
                    "initialize_cursor": True,     # 默认开启
                    "auto_register": True,         # 默认开启
                    "restore_cursor_settings": True, # 默认开启
                    "restore_cursor_workspace": True # 默认开启
                }
            }
        
    def save_config(self, show_info=True):
        """保存配置
        
        Args:
            show_info: 是否显示保存成功提示
        """
        try:
            # 获取配置文件路径
            config_dir = os.path.join(get_app_data_dir(), "config")
            os.makedirs(config_dir, exist_ok=True)
            config_file = os.path.join(config_dir, "quick_function.json")
            
            # 收集当前配置
            config = {
                "delay": self.delay_spinner.value(),
                "stop_on_failure": self.stop_on_failure_switch.isChecked(),
                "functions": {
                    func_id: switch.isChecked()
                    for func_id, switch in self.function_switches.items()
                }
            }
            
            # 保存配置
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            # 仅在需要时显示提示信息
            if show_info:    
                info("快捷一键配置已保存")
                
            return config
        except Exception as e:
            error(f"保存快捷一键配置失败: {str(e)}")
            return None
        
    def on_confirm(self):
        """确认按钮点击事件处理"""
        # 保存配置
        config = self.save_config()
        
        if not config:
            # 配置保存失败
            return
            
        # 关闭配置对话框
        self.accept()
        
        # 获取启用的功能数量
        enabled_functions = [f for f, enabled in config["functions"].items() if enabled]
        
        if not enabled_functions:
            # 没有启用任何功能
            from widgets.dialog import StyledDialog
            StyledDialog.showInfoDialog(
                self.parent(),
                "提示",
                "没有开启任何功能，请至少开启一个功能。",
                "确定"
            )
            return
            
        # 创建进度对话框
        progress_dialog = QuickFunctionProgressDialog(self.parent(), config)
        progress_dialog.start_execution()
        progress_dialog.exec()

    def on_config_changed(self):
        """配置变化时的处理函数，自动保存配置但不显示提示"""
        self.save_config(show_info=False)

    def connect_signals(self):
        """连接所有配置组件的信号"""
        # 连接延迟微调框的值变化信号
        self.delay_spinner.valueChanged.connect(self.on_config_changed)
        
        # 连接失败时停止开关的状态变化信号
        self.stop_on_failure_switch.stateChanged.connect(self.on_config_changed)
        
        # 连接所有功能开关的状态变化信号
        for switch in self.function_switches.values():
            switch.stateChanged.connect(self.on_config_changed)


class QuickFunctionProgressDialog(StyledDialog):
    """快捷一键功能执行对话框
    
    显示快捷一键功能执行状态，采用现代化的卡片流布局，带有动画效果
    """
    
    def __init__(self, parent=None, config=None):
        """初始化对话框
        
        Args:
            parent: 父窗口
            config: 配置信息，包含需要执行的功能和延迟时间
        """
        super().__init__(parent, width=620, height=400)
        self.config = config or {}
        self.worker = None
        self.function_cards = {}  # 存储功能卡片
        self._scroll_animation = None  # 滚动动画对象
        self.last_failed_function = None  # 记录最后一个失败的功能名称
        
        # 加载设置
        self.settings = self.load_settings()
        
        self.init_ui()
    
    def load_settings(self):
        """从settings.json加载设置
        
        Returns:
            dict: 设置项字典
        """
        # 获取设置文件路径
        settings_file = os.path.join(get_app_data_dir(), "settings.json")
        settings = {}
        
        try:
            # 检查文件是否存在
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
            else:
                # 设置文件不存在，使用默认设置
                settings = {
                    "auto_close_dialog_on_success": True
                }
        except Exception as e:
            print(f"加载设置时出错: {str(e)}")
            # 出错时使用默认设置
            settings = {
                "auto_close_dialog_on_success": True
            }
        
        return settings
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建简洁的标题
        header_frame = QFrame()
        header_frame.setStyleSheet(f"""
            QFrame {{
                background-color: transparent;
                border: none;
            }}
        """)
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(0, 0, 0, 15)
        
        title_label = QLabel("快捷一键功能")
        title_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: 16px;
            font-weight: bold;
        """)
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        # 添加计时器标签
        self.time_label = QLabel("00:00")
        self.time_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: 14px;
        """)
        header_layout.addWidget(self.time_label)
        
        # 开始计时
        self.elapsed_time = 0
        self.timer = QTimer(self)
        self.timer.timeout.connect(self.update_time)
        self.timer.start(1000)  # 每秒更新一次
        
        self.addWidget(header_frame)
        
        # 创建功能卡片容器
        cards_container = QWidget()
        cards_container.setStyleSheet(f"""
            QWidget {{
                background-color: transparent;
                border: none;
            }}
        """)
        cards_layout = QVBoxLayout(cards_container)
        cards_layout.setSpacing(10)
        cards_layout.setContentsMargins(0, 0, 0, 0)
        
        # 添加每个启用的功能卡片
        function_info = {
            "save_current_account": ("💾", "保存当前登录账户"),
            "reset_machine_id": ("🔑", "重置机器码"),
            "backup_cursor_settings": ("🔧", "备份Cursor设置"),
            "backup_cursor_workspace": ("📑", "备份Cursor会话记录"),
            "initialize_cursor": ("✨", "初始化Cursor"),
            "auto_register": ("🤖", "自动注册Cursor账号"),
            "restore_cursor_settings": ("🛠️", "恢复Cursor设置"),
            "restore_cursor_workspace": ("📖", "恢复Cursor会话记录")
        }
        
        # 只添加启用的功能卡片
        actual_card_count = 0  # 实际添加的卡片计数
        
        # 先创建所有卡片但不添加到布局中
        for func_id, (emoji, title) in function_info.items():
            if self.config.get("functions", {}).get(func_id, False):
                card = FunctionCard(emoji, title)
                # 默认设置卡片为不可见，但保留位置
                card.setVisible(False)
                card.set_opacity(0.0)
                
                # 先将卡片添加到布局，但它们是不可见的
                cards_layout.addWidget(card)
                self.function_cards[func_id] = card
                
                # 使用实际卡片计数计算延迟，每张卡片之间延迟150ms
                # 第一张卡片也有短暂延迟，让界面有时间完全加载
                QTimer.singleShot(180 + actual_card_count * 150, lambda c=card: self._start_card_animation(c))
                actual_card_count += 1
        
        # 如果没有启用任何功能，显示提示
        if not self.function_cards:
            empty_label = QLabel("没有启用任何功能")
            empty_label.setStyleSheet(f"""
                font-size: {Theme.FONT_SIZE_TITLE};
                color: {Theme.TEXT_SECONDARY};
                padding: 40px;
                background-color: {Theme.CARD_LEVEL_1};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
            """)
            empty_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            cards_layout.addWidget(empty_label)
        
        # 末尾添加弹性空间，确保卡片紧凑排列在顶部
        cards_layout.addStretch()
        
        # 创建滚动区域，并保存为实例变量
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setWidget(cards_container)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAlwaysOff) # 隐藏垂直滚动条
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff) # 隐藏水平滚动条 (以防万一)
        self.scroll_area.setStyleSheet(f"""
            QScrollArea {{
                background-color: transparent;
                border: none;
            }}
            /* 移除之前为滚动条定义的样式，因为它们不再显示 */
        """)
        
        # 设置最小高度以确保滚动区域占据足够空间
        self.scroll_area.setMinimumHeight(250)
        
        self.addWidget(self.scroll_area)
        
        # 创建底部状态区域
        status_frame = QFrame()
        status_frame.setStyleSheet(f"""
            QFrame {{
                background-color: transparent;
                border: none;
                padding: 5px 0;
            }}
        """)
        status_layout = QVBoxLayout(status_frame)
        status_layout.setSpacing(5)
        status_layout.setContentsMargins(0, 5, 0, 0)
        
        # 移除进度条，只保留状态文本和提示
        
        # 添加提示和进度文本
        info_layout = QHBoxLayout()
        info_layout.setContentsMargins(0, 0, 0, 0)
        
        # 取消提示
        cancel_tip = QLabel("按 ESC 键取消")
        cancel_tip.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.TEXT_SECONDARY};
        """)
        info_layout.addWidget(cancel_tip)
        
        info_layout.addStretch()
        
        # 进度文本
        self.progress_text = QLabel("准备中...")
        self.progress_text.setStyleSheet(f"""
            font-size: {Theme.FONT_SIZE_SMALL};
            color: {Theme.ACCENT};
            font-weight: bold;
        """)
        info_layout.addWidget(self.progress_text)
        
        status_layout.addLayout(info_layout)
        
        self.addWidget(status_frame)
    
    def update_time(self):
        """更新计时器显示"""
        self.elapsed_time += 1
        minutes = self.elapsed_time // 60
        seconds = self.elapsed_time % 60
        self.time_label.setText(f"{minutes:02d}:{seconds:02d}")
    
    def on_progress_updated(self, current, total):
        """更新进度状态
        
        Args:
            current: 当前进度
            total: 总进度
        """
        # 移除对进度条的更新，只更新文本
        self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ACCENT}; font-weight: bold;")
        self.progress_text.setText(f"进度: {current}%")
    
    def start_execution(self):
        """开始执行功能"""
        # 创建工作线程
        self.worker = QuickFunctionWorker(self, self.config)
        
        # 连接信号
        self.worker.progress_updated.connect(self.on_progress_updated)
        self.worker.function_started.connect(self.on_function_started)
        self.worker.function_completed.connect(self.on_function_completed)
        self.worker.log_produced.connect(self.on_log_produced)  # 仍然保留但不显示
        self.worker.all_completed.connect(self.on_all_completed)
        # 连接请求启动Cursor的信号
        self.worker.request_start_cursor.connect(self.on_request_start_cursor)
        
        # 启动工作线程前先更新UI状态
        # 设置初始状态文本和样式
        self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ACCENT}; font-weight: bold;")
        self.progress_text.setText("准备中...")
        
        # 启动工作线程
        self.worker.start()
        
    def on_request_start_cursor(self):
        """处理请求启动Cursor的信号
        
        这个方法会在主线程中安全地启动Cursor应用
        使用QTimer延迟执行，确保文件操作完成并且系统有时间完成资源释放
        """
        from PySide6.QtCore import QTimer
        
        # 更新UI状态
        self.progress_text.setText("即将启动Cursor（请稍候1-2秒）...")
        
        if self.worker:
            self.worker.log_produced.emit("初始化Cursor成功，将在1.5秒后启动应用以确保文件操作完成", "info")
            self.worker.log_produced.emit("注意：为确保系统稳定性，启动过程需要短暂延迟", "info")
        
        # 延迟1.5秒后安全启动Cursor
        QTimer.singleShot(1500, self._safe_start_cursor)
    
    def _safe_start_cursor(self):
        """安全地启动Cursor应用，在主线程中执行
        
        将启动过程包装在try-except中，以确保即使启动失败也不会导致GUI崩溃
        """
        try:
            self.progress_text.setText("正在启动Cursor...")
            
            # 获取主窗口
            main_window = self.parent().window()
            
            # 启动Cursor应用
            result = main_window.start_cursor_app()
            if result:
                self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.SUCCESS}; font-weight: bold;")
                self.progress_text.setText("成功启动Cursor")
            else:
                self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ERROR}; font-weight: bold;")
                self.progress_text.setText("启动Cursor返回失败，但未抛出异常")
        except Exception as e:
            self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ERROR}; font-weight: bold;")
            self.progress_text.setText(f"启动Cursor时发生异常: {str(e)}")
            
            # 将异常信息记录到日志
            if self.worker:
                self.worker.log_produced.emit(f"安全启动Cursor时发生异常: {str(e)}", "error")
    
    def smooth_scroll_to_widget(self, widget, margin=15, duration=800):
        """平滑滚动到指定的小部件
        
        Args:
            widget: 目标小部件
            margin: 距离边缘的预留空间(像素)
            duration: 动画持续时间(毫秒)
        """
        # 获取滚动区域的垂直滚动条
        scroll_bar = self.scroll_area.verticalScrollBar()
        
        # 获取widget在滚动区域中的位置
        widget_pos = widget.mapTo(self.scroll_area.widget(), QPoint(0, 0))
        
        # 计算需要滚动到的位置
        # 要确保widget在可视范围内，需要考虑widget的位置和高度
        target_pos = widget_pos.y() - margin
        
        # 确保目标位置有效（不小于0，不大于最大滚动值）
        target_pos = max(0, min(target_pos, scroll_bar.maximum()))
        
        # 创建或重用滚动动画对象
        if not self._scroll_animation:
            from smooth_scroll_animation import SmoothScrollAnimation
            self._scroll_animation = SmoothScrollAnimation(scroll_bar)
        
        # 设置动画时间和缓动曲线
        self._scroll_animation._animation.setDuration(duration)
        # OutCubic 或 InOutQuint 提供更平滑的滚动体验
        self._scroll_animation._animation.setEasingCurve(QEasingCurve.Type.InOutQuint)
        
        # 获取滚动距离，调整动画时间
        current_pos = scroll_bar.value()
        scroll_distance = abs(target_pos - current_pos)
        
        # 根据滚动距离动态调整动画时间，使短距离滚动更快，长距离保持平滑
        if scroll_distance < 100:
            # 短距离使用较短的动画时间
            adjusted_duration = int(duration * 0.6)
        elif scroll_distance > 500:
            # 长距离使用更长的动画时间确保平滑
            adjusted_duration = int(duration * 1.2)
        else:
            # 中等距离使用标准时间
            adjusted_duration = duration
            
        self._scroll_animation._animation.setDuration(adjusted_duration)
        
        # 开始滚动动画
        self._scroll_animation.scroll_to(target_pos)
        
        # 滚动完成后添加高亮效果
        def add_highlight_effect():
            # 为小部件添加临时的高亮效果
            original_style = widget.styleSheet()
            # 使用亮一点的颜色作为高亮背景，并添加强调色边框
            highlight_style = f"""
                border: 1px solid {Theme.ACCENT} !important;
                background-color: rgba(43, 157, 124, 0.15) !important;
            """
            widget.setStyleSheet(original_style + highlight_style)
            
            # 恢复原样式
            QTimer.singleShot(1000, lambda: widget.setStyleSheet(original_style))
        
        # 动画完成时添加高亮效果
        try:
            self._scroll_animation._animation.finished.disconnect()  # 断开之前的连接
        except:
            pass  # 忽略第一次调用时没有连接的情况
        self._scroll_animation._animation.finished.connect(add_highlight_effect)
    
    def on_function_started(self, function_name):
        """功能开始执行时的处理
        
        Args:
            function_name: 功能名称
        """
        function_id = None
        for func_id, name in self._get_function_mapping().items():
            if name == function_name:
                function_id = func_id
                break
        
        if function_id and function_id in self.function_cards:
            card = self.function_cards[function_id]
            card.set_status(FunctionStatus.RUNNING)
            # 设置运行状态文本和样式
            self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ACCENT}; font-weight: bold;")
            self.progress_text.setText(f"正在执行: {function_name}")
            
            # 使用平滑滚动效果滚动到当前执行的卡片
            QTimer.singleShot(50, lambda: self.smooth_scroll_to_widget(card, margin=15, duration=800))
    
    def on_function_completed(self, function_name, success):
        """功能执行完成时的处理
        
        Args:
            function_name: 功能名称
            success: 是否成功
        """
        # 查找对应的功能ID
        function_id = None
        for func_id, name in self._get_function_mapping().items():
            if name == function_name:
                function_id = func_id
                break
        
        if function_id and function_id in self.function_cards:
            # 更新卡片状态为"成功"或"失败"
            if success:
                message = ""
                # 如果是自动注册功能且有注册的邮箱，添加到消息中
                if function_id == "auto_register" and hasattr(self.worker, "registered_email") and self.worker.registered_email:
                    message = f"{self.worker.registered_email}"
                
                # 设置卡片状态为成功，并显示消息
                self.function_cards[function_id].set_status(FunctionStatus.SUCCESS, message)
                # 设置成功状态文本和样式
                self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ACCENT}; font-weight: bold;")
                self.progress_text.setText(f"{function_name} 执行成功")
            else:
                # 设置卡片状态为失败
                self.function_cards[function_id].set_status(FunctionStatus.FAILED)
                # 设置失败状态文本和样式（使用红色）
                self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ERROR}; font-weight: bold;")
                
                # 特殊处理：如果是初始化Cursor失败
                if function_id == "initialize_cursor":
                    error_message = f"{function_name} 执行失败。注意：通过一键快捷初始化Cursor可能导致某些系统崩溃，请尝试直接在功能页面中执行该操作。"
                    self.progress_text.setText(error_message)
                    # 添加特殊日志
                    from logger import warning
                    warning("一键快捷中的初始化Cursor失败。某些系统可能在一键快捷中执行初始化Cursor时会崩溃，请尝试直接在功能页面中执行该操作。")
                else:
                    self.progress_text.setText(f"{function_name} 执行失败")
                
                # 记录最后一个失败的功能名称
                self.last_failed_function = function_name
    
    def on_log_produced(self, text, log_type):
        """处理日志信息 - 在新设计中不显示详细日志，但仍保留方法以兼容工作线程
        
        Args:
            text: 日志文本
            log_type: 日志类型
        """
        # 在新设计中，我们不显示详细日志
        # 但可以根据需要在调试模式下打印到控制台
        if log_type == "error":
            print(f"[ERROR] {text}")
            
        # 将日志消息写入系统日志，使其显示在日志页面
        log_message = f"[快捷一键] {text}"
        if log_type == "error":
            error(log_message)
        elif log_type == "warning":
            warning(log_message)
        elif log_type == "success":
            info(log_message)  # 使用info级别记录成功消息
        elif log_type == "debug":
            debug(log_message)
        else:
            info(log_message)  # 默认使用info级别
    
    def on_all_completed(self, success):
        """所有功能执行完成的处理
        
        Args:
            success: 是否成功
        """
        # 停止计时器
        self.timer.stop()
        
        # 更新状态文本
        if success:
            # 重置文本样式
            self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ACCENT}; font-weight: bold;")
            self.progress_text.setText(f"全部完成 ({self.time_label.text()})")
            
            # 成功完成：根据设置决定是否自动关闭
            auto_close = self.settings.get("auto_close_dialog_on_success", True)
            if auto_close:
                # 如果设置为自动关闭，延时5秒后关闭
                QTimer.singleShot(5000, self.on_auto_close)
            else:
                # 如果设置为不自动关闭，则不设置定时器
                self.progress_text.setText(f"全部完成 ({self.time_label.text()}) - 按ESC关闭")
        else:
            # 执行失败：检查是否是因为功能失败导致的中断
            if self.last_failed_function and self.config.get("stop_on_failure", True):
                # 设置红色错误提示文本
                self.progress_text.setText(f"已停止后续操作，请单独运行 {self.last_failed_function} 功能检查错误")
                self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ERROR}; font-weight: bold;")
            else:
                # 普通失败或用户取消
                self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ACCENT}; font-weight: bold;")
                self.progress_text.setText(f"执行中断 ({self.time_label.text()}) - 按ESC关闭")
    
    def on_auto_close(self):
        """自动关闭对话框"""
        # 仅在工作线程已完成且当前对话框仍然可见时关闭
        if (not self.worker or not self.worker.isRunning()) and self.isVisible():
            self.accept()
    
    def keyPressEvent(self, event):
        """处理按键事件，支持ESC键取消执行
        
        Args:
            event: 按键事件
        """
        if event.key() == Qt.Key.Key_Escape:
            # 如果工作线程正在运行，停止它
            if self.worker and self.worker.isRunning():
                self.worker.stop()
                
                # 给用户一个取消反馈
                for card in self.function_cards.values():
                    if card.status == FunctionStatus.WAITING:
                        card.set_status(FunctionStatus.FAILED, "已取消")
                
                # 更新状态文本，重置样式为默认
                self.progress_text.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.ACCENT}; font-weight: bold;")
                self.progress_text.setText("已取消 - 即将自动关闭")
                
                # 用户取消操作后2秒自动关闭
                QTimer.singleShot(2000, self.on_auto_close)
            else:
                # 如果工作线程没有运行，直接关闭对话框
                self.reject()
        else:
            # 其他按键，调用父类方法处理
            super().keyPressEvent(event)
    
    def _get_function_mapping(self):
        """获取功能ID和名称的映射
        
        Returns:
            dict: 功能ID和名称的映射
        """
        return {
            "save_current_account": "保存当前登录账户",
            "reset_machine_id": "重置机器码",
            "backup_cursor_settings": "备份Cursor设置",
            "backup_cursor_workspace": "备份Cursor会话记录",
            "initialize_cursor": "初始化Cursor",
            "auto_register": "自动注册Cursor账号",
            "restore_cursor_settings": "恢复Cursor设置",
            "restore_cursor_workspace": "恢复Cursor会话记录"
        }

    def _start_card_animation(self, card):
        """启动卡片的入场动画
        
        Args:
            card: 要启动动画的卡片
        """
        # 先恢复卡片可见性和高度，但保持透明
        card.setVisible(True)
        card.setFixedHeight(55)
        
        # 确保卡片初始状态设置正确
        card._offset_x = 30  # 从右侧进入
        card._offset_y = 5   # 微小的Y轴偏移
        card._scale = 0.95   # 初始稍微缩小
        card.set_opacity(0.0)  # 完全透明
        
        # 重置动画起始值
        card._offset_x_animation.setStartValue(card._offset_x)
        card._offset_y_animation.setStartValue(card._offset_y)
        card._scale_animation.setStartValue(card._scale)
        
        # 启动入场动画
        card.start_entrance_animation()