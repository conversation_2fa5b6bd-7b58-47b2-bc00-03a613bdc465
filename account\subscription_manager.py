#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
订阅管理器模块
提供Cursor订阅查询和取消功能
"""

import requests
import json
import re
from datetime import datetime
from logger import info, error, warning


class CursorSubscriptionManager:
    """Cursor订阅管理器"""
    
    def __init__(self, account_data):
        """初始化订阅管理器
        
        Args:
            account_data: 账户数据字典，包含email和auth_info
        """
        self.account_data = account_data
        self.email = account_data.get("email", "")
        self.session = requests.Session()
        
        # 从账户数据中获取token
        self.cursor_token = self._get_cursor_token()
        
        # 订阅相关信息
        self.stripe_session_url = None
        self.live_session_id = None
        self.bps_session_id = None
        self.subscription_id = None
        self.csrf_token = None
        self.bearer_token = None
        
    def _get_cursor_token(self):
        """从账户数据中获取cursor token"""
        try:
            # 固定前缀
            fixed_prefix = "user_01000000000000000000000000%3A%3A"
            
            # 从auth_info中获取accessToken
            auth_info = self.account_data.get('auth_info', {})
            access_token = auth_info.get('cursorAuth/accessToken')
            
            if not access_token:
                error(f"账户 {self.email} 缺少accessToken")
                return None
                
            # 构建完整的token
            full_token = fixed_prefix + access_token
            info(f"成功构建账户 {self.email} 的token")
            return full_token
            
        except Exception as e:
            error(f"获取账户 {self.email} token时出错: {str(e)}")
            return None
    
    def get_stripe_session_url(self):
        """获取Stripe会话URL"""
        if not self.cursor_token:
            error("缺少cursor token，无法获取Stripe会话")
            return False
            
        cookies = {
            'NEXT_LOCALE': 'en',
            'WorkosCursorSessionToken': self.cursor_token
        }
        
        headers = {
            'accept': '*/*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'if-none-match': '"zc6lt7pm693k"',
            'priority': 'u=1, i',
            'referer': 'https://cursor.com/dashboard',
            'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
            'sec-ch-ua-arch': 'x86',
            'sec-ch-ua-bitness': '64',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': 'Windows',
            'sec-ch-ua-platform-version': '19.0.0',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
        }
        
        try:
            response = self.session.get('https://cursor.com/api/stripeSession', 
                                      cookies=cookies, headers=headers, timeout=15)
            if response.status_code == 200:
                stripe_url = response.text.strip().replace('"', '')
                self.stripe_session_url = stripe_url
                # 从URL中提取live session_id
                url_parts = stripe_url.split('/')
                if 'session' in url_parts:
                    session_index = url_parts.index('session')
                    if session_index + 1 < len(url_parts):
                        self.live_session_id = url_parts[session_index + 1]
                info(f"成功获取账户 {self.email} 的Stripe会话")
                return True
            else:
                error(f"获取账户 {self.email} Stripe会话失败: {response.status_code}")
                return False
        except Exception as e:
            error(f"获取账户 {self.email} Stripe会话异常: {str(e)}")
            return False
    
    def get_session_info(self):
        """获取会话信息，包括Bearer token和真实的bps session ID"""
        if not self.live_session_id:
            error("没有live_session_id，请先获取Stripe会话")
            return False
            
        try:
            response = self.session.get(self.stripe_session_url, timeout=15)
            if response.status_code == 200:
                html_content = response.text
                
                # 从cookies中提取CSRF token
                for cookie in self.session.cookies:
                    if 'csrf' in cookie.name.lower():
                        self.csrf_token = cookie.value
                        break
                
                # 从页面HTML中提取session_api_key (Bearer token)
                bearer_patterns = [
                    r'"session_api_key":"(ek_live_[^"]+)"',
                    r'&quot;session_api_key&quot;:&quot;(ek_live_[^&]+)&quot;',
                    r'session_api_key["\']?\s*:\s*["\']?(ek_live_[^"\']+)["\']?'
                ]
                
                for pattern in bearer_patterns:
                    match = re.search(pattern, html_content)
                    if match:
                        self.bearer_token = match.group(1)
                        break
                
                # 从页面HTML中提取portal_session_id (真实的bps_ session ID)
                bps_patterns = [
                    r'"portal_session_id":"(bps_[^"]+)"',
                    r'&quot;portal_session_id&quot;:&quot;(bps_[^&]+)&quot;',
                    r'portal_session["\']?\s*:\s*["\']?(bps_[^"\']+)["\']?',
                    r'"id":"(bps_[^"]+)".*"object":"billing_portal\.session"'
                ]
                
                for pattern in bps_patterns:
                    match = re.search(pattern, html_content, re.DOTALL)
                    if match:
                        self.bps_session_id = match.group(1)
                        break
                
                if not self.bearer_token:
                    warning("未找到Bearer token")
                    return False
                    
                if not self.bps_session_id:
                    warning("未找到BPS Session ID，尝试通过API获取...")
                    return self.get_bps_session_from_api()
                
                return True
            else:
                error(f"访问Stripe会话页面失败: {response.status_code}")
                return False
        except Exception as e:
            error(f"获取会话信息异常: {str(e)}")
            return False
    
    def get_bps_session_from_api(self):
        """通过API获取真实的bps session ID"""
        if not self.bearer_token:
            error("缺少Bearer token")
            return False
            
        possible_urls = [
            f"https://billing.stripe.com/v1/billing_portal/sessions/{self.live_session_id}",
            "https://billing.stripe.com/v1/billing_portal/sessions"
        ]
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }
        
        for url in possible_urls:
            try:
                response = self.session.get(url, headers=headers, timeout=15)
                if response.status_code == 200:
                    data = response.json()
                    if 'id' in data and data['id'].startswith('bps_'):
                        self.bps_session_id = data['id']
                        info(f"通过API获取到BPS Session ID")
                        return True
            except Exception as e:
                warning(f"API请求异常 {url}: {str(e)}")
        
        return False
    
    def get_subscription_status(self):
        """获取详细的订阅状态信息"""
        if not self.bps_session_id or not self.bearer_token:
            error("缺少必要的会话信息")
            return None
            
        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions"
        
        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'referer': self.stripe_session_url,
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x-requested-with': 'XMLHttpRequest'
        }
        
        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token
        
        params = {
            'expand[]': [
                'data.items.price_details'
            ],
            'include_only[]': [
                'has_more',
                'data.id',
                'data.status',
                'data.created',
                'data.current_period_end',
                'data.trial_end',
                'data.cancel_at',
                'data.cancel_at_period_end',
                'data.is_cancelable',
                'data.items.id',
                'data.items.quantity',
                'data.items.price_details.unit_amount',
                'data.items.price_details.currency',
                'data.items.price_details.recurring.interval',
                'data.items.price_details.product.name'
            ]
        }
        
        try:
            response = self.session.get(url, headers=headers, params=params, timeout=15)
            if response.status_code == 200:
                data = response.json()
                return self.parse_subscription_data(data)
            else:
                error(f"获取订阅状态失败: {response.status_code}")
                return None
        except Exception as e:
            error(f"获取订阅状态异常: {str(e)}")
            return None
    
    def parse_subscription_data(self, data):
        """解析订阅数据"""
        if 'data' not in data or len(data['data']) == 0:
            return {
                'has_subscription': False,
                'status': '未订阅',
                'message': '没有找到任何订阅'
            }
        
        subscription = data['data'][0]
        
        # 基本信息
        status = subscription.get('status', 'unknown')
        subscription_id = subscription.get('id', 'N/A')
        created = subscription.get('created')
        
        # 取消状态
        cancel_at = subscription.get('cancel_at')
        cancel_at_period_end = subscription.get('cancel_at_period_end', False)
        is_scheduled_for_cancellation = cancel_at or cancel_at_period_end
        is_cancelable = subscription.get('is_cancelable', False)
        
        # 时间信息
        trial_end = subscription.get('trial_end')
        current_period_end = subscription.get('current_period_end')
        
        # 产品信息
        items = subscription.get('items', [])
        product_info = []
        if items:
            for item in items:
                price_details = item.get('price_details', {})
                product = price_details.get('product', {})
                product_name = product.get('name', 'Unknown Product')
                unit_amount = price_details.get('unit_amount', 0)
                currency = price_details.get('currency', 'usd').upper()
                recurring = price_details.get('recurring', {})
                interval = recurring.get('interval', 'month')
                quantity = item.get('quantity', 1)
                
                price = unit_amount / 100
                product_info.append({
                    'name': product_name,
                    'price': price,
                    'currency': currency,
                    'interval': interval,
                    'quantity': quantity
                })
        
        # 保存subscription_id用于取消操作
        if is_cancelable:
            self.subscription_id = subscription_id
        
        return {
            'has_subscription': True,
            'status': status,
            'subscription_id': subscription_id,
            'created': created,
            'trial_end': trial_end,
            'current_period_end': current_period_end,
            'cancel_at': cancel_at,
            'cancel_at_period_end': cancel_at_period_end,
            'is_scheduled_for_cancellation': is_scheduled_for_cancellation,
            'is_cancelable': is_cancelable,
            'product_info': product_info,
            'stripe_session_url': self.stripe_session_url
        }

    def get_subscription_id(self):
        """获取订阅ID"""
        if not self.bps_session_id or not self.bearer_token:
            error("缺少必要的会话信息")
            return False

        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions"

        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'referer': self.stripe_session_url,
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x-requested-with': 'XMLHttpRequest'
        }

        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token

        params = {
            'include_only[]': [
                'has_more',
                'data.id',
                'data.status',
                'data.is_cancelable'
            ]
        }

        try:
            response = self.session.get(url, headers=headers, params=params, timeout=15)
            if response.status_code == 200:
                data = response.json()
                if 'data' in data and len(data['data']) > 0:
                    for subscription in data['data']:
                        if subscription.get('is_cancelable', False):
                            self.subscription_id = subscription['id']
                            info(f"找到可取消的订阅: {self.subscription_id}")
                            return True
                    warning("没有找到可取消的订阅")
                    return False
                else:
                    warning("没有找到订阅数据")
                    return False
            else:
                error(f"获取订阅列表失败: {response.status_code}")
                return False
        except Exception as e:
            error(f"获取订阅ID异常: {str(e)}")
            return False

    def cancel_subscription(self, refund=False):
        """取消订阅"""
        if not all([self.bps_session_id, self.subscription_id, self.bearer_token]):
            error("缺少必要的参数")
            return False

        url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions/{self.subscription_id}/cancel"

        headers = {
            'accept': 'application/json',
            'authorization': f'Bearer {self.bearer_token}',
            'accept-language': 'zh-Hans, zh-CN',
            'browser-language': 'zh-CN',
            'content-type': 'application/x-www-form-urlencoded',
            'origin': 'https://billing.stripe.com',
            'referer': f"https://billing.stripe.com/p/session/{self.live_session_id}/subscriptions/{self.subscription_id}/cancel",
            'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
            'stripe-livemode': 'true',
            'stripe-version': '2025-04-30.basil',
            'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'x-requested-with': 'XMLHttpRequest'
        }

        if self.csrf_token:
            headers['x-stripe-csrf-token'] = self.csrf_token

        params = {
            'include_only[]': 'id'
        }

        data = {
            'refund': 'true' if refund else 'false'
        }

        try:
            info(f"开始取消账户 {self.email} 的订阅...")
            response = self.session.post(url, headers=headers, params=params, data=data, timeout=15)

            if response.status_code == 200:
                result = response.json()
                if 'id' in result:
                    info(f"账户 {self.email} 订阅取消成功!")
                    return True
                else:
                    error("取消订阅响应格式异常")
                    return False
            else:
                error(f"取消订阅失败: {response.status_code}")
                try:
                    error_data = response.json()
                    error(f"错误详情: {json.dumps(error_data, indent=2)}")
                except:
                    error(f"错误响应: {response.text}")
                return False

        except Exception as e:
            error(f"取消订阅异常: {str(e)}")
            return False

    def view_subscription_status_flow(self):
        """查看订阅状态的完整流程"""
        info(f"开始查看账户 {self.email} 的订阅状态...")

        # 步骤1: 获取Stripe会话URL
        if not self.get_stripe_session_url():
            return None

        # 步骤2: 获取会话信息（Bearer token和BPS session ID）
        if not self.get_session_info():
            return None

        # 步骤3: 获取订阅状态
        return self.get_subscription_status()

    def cancel_subscription_flow(self, refund=False):
        """完整的取消订阅流程"""
        info(f"开始取消账户 {self.email} 的订阅...")

        # 步骤1: 获取Stripe会话URL
        if not self.get_stripe_session_url():
            return False

        # 步骤2: 获取会话信息（Bearer token和BPS session ID）
        if not self.get_session_info():
            return False

        # 步骤3: 获取订阅ID
        if not self.get_subscription_id():
            return False

        # 步骤4: 取消订阅
        return self.cancel_subscription(refund)
