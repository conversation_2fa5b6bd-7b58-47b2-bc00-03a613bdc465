#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动注册对话框模块
提供Cursor账号自动注册的对话框界面和后台处理
"""

import os
import sys
import json
import time
import tempfile
import re
import io
import threading
import requests
from contextlib import contextmanager
from PySide6.QtWidgets import (
    QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QWidget, QFrame, QPlainTextEdit, QApplication, QMenu, QMainWindow
)
from PySide6.QtCore import Qt, Signal, QThread, QTimer, QTime
from PySide6.QtGui import QColor

from widgets.dialog import StyledDialog
from widgets.styled_widgets import StyledButton, StyledProgressBar
from widgets.toast import ToastMessage
from theme import Theme
from utils import get_app_data_dir
from logger import info, warning, error, debug
from account.account_data import AccountData
import subprocess
import importlib.util
import importlib
import builtins
import uuid
import glob
import traceback
import datetime

# 导入cursor_auto模块的颜色类，用于邮件内容处理
try:
    # 使用标准导入
    from core.cursor_auto import config  # type: ignore
    Colors = config.Colors
except ImportError:
    # 如果导入失败，定义一个简单的替代类
    class Colors:
        """替代的颜色类"""
        RED = ''
        GREEN = ''
        YELLOW = ''
        BLUE = ''
        PURPLE = ''
        CYAN = ''
        WHITE = ''
        NC = ''

# 创建一个全局锁，用于线程安全的输出
output_lock = threading.Lock()

class AutoRegisterWorker(QThread):
    """自动注册的工作线程"""
    
    # 定义信号
    progress_updated = Signal(int, int)  # 参数为当前进度和总进度
    log_produced = Signal(str, str)  # 参数为日志文本和日志类型 (info, warning, error)
    operation_finished = Signal(bool, str)  # 参数为是否成功和结果信息
    manual_cf_required = Signal()  # 需要手动CF验证的信号
    manual_card_required = Signal()  # 需要手动绑卡确认的信号
    
    # 日志类型映射
    LOG_TYPE_MAPPING = {
        "info": "info",
        "warning": "warning",
        "error": "error",
        "success": "success",
        "process": "process",
        "debug": "debug",
    }
    
    def __init__(self, settings=None):
        super().__init__()
        self.settings = settings or {}
        # 恢复 auto_dir 的初始化，并指向新的 cursor_auto 目录
        self.auto_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), "core", "cursor_auto")
        self.temp_env_file = None
        self._stop_flag = False
        self.browser_manager = None
        self.browser = None
        self.register_only = False  # 添加仅注册标志，默认为False
        self.register_method = 1    # 添加注册方案，默认为方案一
        self.start_time = 0  # 添加开始时间变量，用于计算注册耗时
        self.cursor_killed = False  # 添加变量，记录是否关闭了Cursor
        
        # 添加状态跟踪变量
        self.registration_state = "unknown"  # 可能的值: unknown, success, failed
        self.failure_reason = ""  # 如果失败，记录原因
        self.cancel_subscription_result = None  # 取消订阅结果
        
        # 用于保存系统输出重定向前的标准输出和标准错误
        self._original_stdout = None
        self._original_stderr = None
        self._stdout_buffer = None
        self._stderr_buffer = None
        
        # 记录是否已经在前一个输出中处理了具体的内容部分
        self._has_pending_box_content = False
        self._current_log_type = "info"
        self._current_box_title = ""
        self._current_box_content = []
        
        # 用于跟踪最近处理过的日志消息，防止重复显示
        self._recent_log_messages = set()
        self._log_message_max_size = 100  # 最多保存100条消息记录

        # 手动CF验证事件
        self.manual_cf_event = threading.Event()

        # 手动绑卡确认事件
        self.manual_card_event = threading.Event()
    
    @contextmanager
    def _redirect_stdout(self):
        """重定向标准输出和标准错误"""
        self._original_stdout = sys.stdout
        self._original_stderr = sys.stderr
        self._stdout_buffer = io.StringIO()
        self._stderr_buffer = io.StringIO()
        
        sys.stdout = self._stdout_buffer
        sys.stderr = self._stderr_buffer
        
        try:
            yield
        finally:
            # 恢复标准输出和标准错误
            sys.stdout = self._original_stdout
            sys.stderr = self._original_stderr
    
    def _process_redirected_output(self):
        """处理重定向的输出"""
        with output_lock:  # 使用锁以确保线程安全
            # 处理标准输出
            stdout_content = self._stdout_buffer.getvalue()
            if stdout_content:
                self._process_output_lines(stdout_content.splitlines())
                # 清空缓冲区
                self._stdout_buffer.truncate(0)
                self._stdout_buffer.seek(0)
            
            # 处理标准错误
            stderr_content = self._stderr_buffer.getvalue()
            if stderr_content:
                # 将标准错误的输出视为错误日志
                for line in stderr_content.splitlines():
                    if line.strip():
                        self.log_produced.emit(line.strip(), "error")
                # 清空缓冲区
                self._stderr_buffer.truncate(0)
                self._stderr_buffer.seek(0)
            
            # 如果有挂起的内容框内容，发送它
            if self._has_pending_box_content:
                self._send_pending_box_content()
    
    def _process_output_lines(self, lines):
        """处理输出中的行"""
        for line in lines:
            # 如果是空行且有挂起的内容框内容，发送它
            if not line.strip() and self._has_pending_box_content:
                self._send_pending_box_content()
                continue
            
            # 如果是验证码相关的消息，检查是否重复
            line_stripped = line.strip()
            
            # 检查是否是邮件内容相关的行
            is_email_content = "验证码邮件内容" in line_stripped or "邮件内容:" in line_stripped or "邮件正文:" in line_stripped
            
            # 对于邮件内容，我们不做去重处理，直接显示所有内容
            if is_email_content:
                # 如果有挂起的内容框内容，先发送它
                if self._has_pending_box_content:
                    self._send_pending_box_content()
                
                # 邮件内容标题处理
                log_type = "info"
                if line_stripped.startswith("["):
                    # 提取并发送标题
                    self.log_produced.emit(self._strip_log_prefix(line_stripped), log_type)
                    
                    # 开始新的内容框用于后续的邮件内容
                    self._current_log_type = log_type
                    self._current_box_title = line_stripped
                    self._current_box_content = []
                    self._has_pending_box_content = True
                else:
                    # 直接发送邮件内容行
                    self.log_produced.emit(line_stripped, "info")
                
                continue
            
            # 处理常规验证码相关消息
            is_verification_related = any(keyword in line_stripped for keyword in 
                                      ["正在获取邮箱验证码", "验证码匹配规则", "从临时邮箱获取邮件列表", 
                                       "获取邮件内容", "成功从临时邮箱获取验证码", "正在输入验证码", 
                                       "验证码输入完成"])
            
            if is_verification_related:
                # 使用消息内容作为键，检查是否已经发送过
                content_key = self._strip_log_prefix(line_stripped)
                if content_key in self._recent_log_messages:
                    # 重复消息，跳过
                    continue
                    
                # 将新消息添加到集合
                self._recent_log_messages.add(content_key)
                
                # 如果集合大小超过限制，移除最旧的消息
                if len(self._recent_log_messages) > self._log_message_max_size:
                    # 由于set不支持直接索引，使用pop移除任一元素
                    self._recent_log_messages.pop()
            
            # 处理print_box输出的标题行
            if re.match(r'^\s*\[.*\]\s+.*$', line):  # 匹配可能的标题行 [xxx] yyy
                # 如果有挂起的内容框内容，发送它
                if self._has_pending_box_content:
                    self._send_pending_box_content()
                
                # 提取标题和日志类型
                log_type = "info"
                title = line.strip()
                
                # 尝试根据前缀判断日志类型
                type_prefixes = {
                    "[错误]": "error",
                    "[警告]": "warning",
                    "[信息]": "info",
                    "[成功]": "success",
                    "[进行]": "process",
                    "[调试]": "debug",
                    "[提示]": "info",
                    # 英文版本
                    "[ERROR]": "error",
                    "[WARNING]": "warning",
                    "[INFO]": "info",
                    "[SUCCESS]": "success",
                    "[PROCESS]": "process",
                    "[DEBUG]": "debug",
                    "[TIP]": "info",
                }
                
                for prefix, type_name in type_prefixes.items():
                    if prefix in title:
                        log_type = type_name
                        break
                
                # 开始新的内容框
                self._current_log_type = log_type
                self._current_box_title = title
                self._current_box_content = []
                self._has_pending_box_content = True
            
            # 处理内容框中的内容行（缩进行）
            elif line.startswith("  ") and self._has_pending_box_content:
                self._current_box_content.append(line.strip())
            
            # 处理验证码成功提示（特殊处理）
            elif "• 验证码:" in line:
                # 提取验证码
                code_match = re.search(r"• 验证码: (\d+)", line)
                if code_match:
                    code = code_match.group(1)
                    # 检查此验证码消息是否已发送
                    verification_message = f"获取到验证码: {code}"
                    if verification_message not in self._recent_log_messages:
                        self.log_produced.emit(verification_message, "success")
                        self._recent_log_messages.add(verification_message)
            
            # 普通的日志行
            elif line.strip():
                # 检查是否包含已知的日志类型标识
                log_type = "info"
                
                if "[错误]" in line or "[ERROR]" in line:
                    log_type = "error"
                elif "[警告]" in line or "[WARNING]" in line:
                    log_type = "warning"
                elif "[成功]" in line or "[SUCCESS]" in line:
                    log_type = "success"
                elif "[进行]" in line or "[PROCESS]" in line:
                    log_type = "process"
                elif "[调试]" in line or "[DEBUG]" in line:
                    log_type = "debug"
                
                # 去除前缀再发送
                stripped_line = self._strip_log_prefix(line.strip())
                
                # 如果是验证码相关消息，且已处理过，则跳过
                if is_verification_related and stripped_line in self._recent_log_messages:
                    continue
                    
                # 发送消息并记录
                self.log_produced.emit(stripped_line, log_type)
                if is_verification_related:
                    self._recent_log_messages.add(stripped_line)
    
    def _send_pending_box_content(self):
        """发送挂起的内容框内容"""
        if self._current_box_content:
            # 对于带内容的内容框，先发送标题，然后发送每个内容项
            stripped_title = self._strip_log_prefix(self._current_box_title)
            
            # 检查是否是验证码相关标题
            is_verification_related = any(keyword in self._current_box_title for keyword in 
                                      ["正在获取邮箱验证码", "验证码匹配规则", "从临时邮箱获取邮件列表", 
                                       "获取邮件内容", "成功从临时邮箱获取验证码", "正在输入验证码", 
                                       "验证码输入完成"])
            
            # 如果不是重复的验证码相关消息，或者不是验证码相关消息，则发送
            if not is_verification_related or stripped_title not in self._recent_log_messages:
                self.log_produced.emit(stripped_title, self._current_log_type)
                # 如果是验证码相关消息，记录已发送
                if is_verification_related:
                    self._recent_log_messages.add(stripped_title)
            
            # 处理内容项
            for content_line in self._current_box_content:
                # 内容通常不是验证码相关信息的核心部分，直接发送
                self.log_produced.emit(f"  {content_line}", self._current_log_type)
        else:
            # 对于只有标题的内容框，只发送标题
            stripped_title = self._strip_log_prefix(self._current_box_title)
            
            # 检查是否是验证码相关标题
            is_verification_related = any(keyword in self._current_box_title for keyword in 
                                      ["正在获取邮箱验证码", "验证码匹配规则", "从临时邮箱获取邮件列表", 
                                       "获取邮件内容", "成功从临时邮箱获取验证码", "正在输入验证码", 
                                       "验证码输入完成"])
            
            # 如果不是重复的验证码相关消息，或者不是验证码相关消息，则发送
            if not is_verification_related or stripped_title not in self._recent_log_messages:
                self.log_produced.emit(stripped_title, self._current_log_type)
                # 如果是验证码相关消息，记录已发送
                if is_verification_related:
                    self._recent_log_messages.add(stripped_title)
        
        # 重置内容框状态
        self._has_pending_box_content = False
        self._current_box_title = ""
        self._current_box_content = []
        self._current_log_type = "info"
    
    def _strip_log_prefix(self, message):
        """移除日志消息中的 [信息]、[警告] 等前缀"""
        # 定义所有可能的前缀
        prefixes = [
            "[信息] ", "[警告] ", "[错误] ", "[成功] ", "[进行] ", "[调试] ", "[提示] ",
            # 英文版本
            "[INFO] ", "[WARNING] ", "[ERROR] ", "[SUCCESS] ", "[PROCESS] ", "[DEBUG] ", "[TIP] "
        ]
        
        # 检查并移除前缀
        for prefix in prefixes:
            if message.startswith(prefix):
                return message[len(prefix):]
        
        return message
    
    def stop(self):
        """停止线程执行"""
        self.log_produced.emit("接收到停止 AutoRegisterWorker 的请求", "warning")
        self._stop_flag = True
        
        # 如果已经设置了成功状态，则保留该状态
        if self.registration_state == "success":
            self.log_produced.emit("注意：工作线程已经报告成功，将保留成功状态", "warning")
            # 不修改状态，只进行清理
        else:
            # 只有在之前不是成功状态时，才设置为失败
            self.registration_state = "failed"
            self.failure_reason = "用户取消"
        
                # 关闭浏览器
        if self.browser_manager:
            try:
                self.log_produced.emit("浏览器管理器 quit() 调用...", "info")
                self.browser_manager.quit()
                self.log_produced.emit("浏览器管理器 quit() 调用成功", "info")
            except Exception as cleanup_error:
                self.log_produced.emit(f"清理浏览器资源时出错: {str(cleanup_error)}", "error")
        
        # 停止验证码处理
        try:
            self.log_produced.emit("请求停止后尝试执行清理...", "info")
            # 如果存在EmailVerificationHandler实例，尝试停止它
            if hasattr(self, 'email_handler'):
                # 使用内部方法设置停止标志
                if hasattr(self.email_handler, 'set_should_stop'):
                    self.email_handler.set_should_stop(True)
                    # self.log_produced.emit("验证码处理流程停止标志已设置为: True", "info")
        except Exception as cleanup_error:
            self.log_produced.emit(f"清理资源时出错: {str(cleanup_error)}", "error")
            
        # 重置cursor_pro_keep_alive模块状态
        try:
            # 尝试导入并重置模块状态
            from core.cursor_auto import cursor_pro_keep_alive
            # 清理全局工作线程实例
            cursor_pro_keep_alive.worker_instance = None
            if hasattr(cursor_pro_keep_alive, 'reset_module_state'):
                cursor_pro_keep_alive.reset_module_state()
                # self.log_produced.emit("已重置cursor_pro_keep_alive模块状态", "info")
        except Exception as reset_error:
            self.log_produced.emit(f"重置模块状态时出错: {str(reset_error)}", "warning")
            
        self.log_produced.emit("自动注册流程已停止", "warning")
        
        # 只有在保留成功状态的情况下才发送成功信号
        if self.registration_state == "success":
            self.operation_finished.emit(True, "注册成功但用户请求停止后续流程")
        else:
            # 否则发送失败信号
            self.operation_finished.emit(False, "用户取消")
    
    def run(self):
        """线程主函数"""
        try:
            self.start_time = time.time()  # 记录开始时间
            
            # 初始化状态为未知
            self.registration_state = "unknown"
            self.failure_reason = ""
            
            # 发出日志信号，输出自动注册开始信息
            self.log_produced.emit("自动注册任务已启动", "info")
            
            # 加载当前设置
            self.log_produced.emit("正在加载配置...", "info")
            self.progress_updated.emit(5, 100)
            
            # 获取域名配置并记录
            domains_str = self.settings.get("auto_register_domain", "")
            domains = [d.strip() for d in domains_str.split(",") if d.strip()]
            if len(domains) > 1:
                self.log_produced.emit(f"配置了 {len(domains)} 个域名: {domains_str}", "info")
            else:
                self.log_produced.emit(f"使用域名: {domains_str}", "info")
            
            # 获取邮箱配置
            email_type = self.settings.get("auto_register_email_type", "temp")
            
            # 检查停止标志
            if self._stop_flag:
                self.log_produced.emit("自动注册流程已被用户取消", "warning")
                self.registration_state = "failed"
                self.failure_reason = "用户取消"
                self.operation_finished.emit(False, "用户取消")
                return
                
            # 准备环境
            if not self.prepare_environment():
                self.log_produced.emit("准备环境失败，中止注册流程", "error")
                self.registration_state = "failed"
                self.failure_reason = "准备环境失败"
                self.operation_finished.emit(False, "准备环境失败")
                return
            
            # 检查停止标志
            if self._stop_flag:
                self.log_produced.emit("自动注册流程已被用户取消", "warning")
                self.registration_state = "failed"
                self.failure_reason = "用户取消"
                self.operation_finished.emit(False, "用户取消")
                return
                
            # 执行注册
            success = self.execute_auto_register()
            
            # 检查停止标志
            if self._stop_flag:
                self.log_produced.emit("自动注册流程已被用户取消", "warning")
                self.registration_state = "failed"
                self.failure_reason = "用户取消"
                self.operation_finished.emit(False, "用户取消")
                return
                
            # 记录执行结果
            # self.log_produced.emit(f"execute_auto_register 返回结果: {success}", "info")
            # self.log_produced.emit(f"当前状态: {self.registration_state}", "info")
            if self.failure_reason:
                # self.log_produced.emit(f"失败原因: {self.failure_reason}", "info")
                pass
            
            # 最终检查：通过查看账号文件变化来检查成功，但不覆盖实际注册失败的状态
            account_count = 0
            try:
                # 获取主窗口账号数据对象
                from PySide6.QtWidgets import QApplication
                main_window = None
                for widget in QApplication.instance().topLevelWidgets():
                    if hasattr(widget, 'account_data'):
                        main_window = widget
                        break
                
                if main_window and hasattr(main_window, 'account_data'):
                    # 从account.account_data导入AccountData以进行类型检查
                    from account.account_data import AccountData
                    
                    # 检查account_data是否为AccountData实例
                    if isinstance(main_window.account_data, AccountData):
                        if hasattr(main_window.account_data, 'accounts_file'):
                            accounts_file = main_window.account_data.accounts_file
                            if os.path.exists(accounts_file):
                                from account.account_data import AccountData
                                account_data = AccountData()
                                # 重新加载账号文件，确保获取最新状态
                                accounts = account_data.load_accounts()
                                account_count = len(accounts) if accounts else 0
                                # self.log_produced.emit(f"最终检查: 发现 {account_count} 个账号", "info")
                                # 注意：不再自动设置registration_state为success
                        else:
                            # 提供更具体的错误信息
                            self.log_produced.emit("最终账号检查: AccountData实例缺少accounts_file属性", "info")
                    else:
                        # account_data不是预期的类型，提供更详细信息
                        actual_type = type(main_window.account_data).__name__
                        self.log_produced.emit(f"最终账号检查: account_data不是预期的类型(AccountData)，实际类型: {actual_type}", "info")
            except Exception as e:
                self.log_produced.emit(f"最终账号检查出错: {str(e)}", "warning")
                
            # 优先使用execute_auto_register的返回结果来决定最终状态
            # 只有当success为True时才认为是成功
            if success:
                self.log_produced.emit("自动注册成功！", "success")
                self.registration_state = "success"  # 确保状态一致
                self.operation_finished.emit(True, "自动注册成功！")
            else:
                # 提供更详细的失败信息
                failure_message = self.failure_reason if self.failure_reason else "自动注册失败！"
                # self.log_produced.emit(f"自动注册失败！原因: {failure_message}", "error")
                self.registration_state = "failed"  # 确保状态一致
                self.operation_finished.emit(False, failure_message)
                
        except Exception as e:
            import traceback
            error_info = f"自动注册过程中出现异常: {str(e)}\n{traceback.format_exc()}"
            self.log_produced.emit(error_info, "error")
            self.registration_state = "failed"
            self.failure_reason = f"注册过程异常: {str(e)}"
            self.operation_finished.emit(False, f"注册失败: {str(e)}")
        finally:
            # 清理临时文件
            self.cleanup()
    
    def prepare_environment(self):
        """准备注册环境"""
        try:
            self.log_produced.emit("准备注册环境...", "info")
            
            # 创建临时.env文件
            self.log_produced.emit("创建临时配置文件...", "info")
            
            # 获取应用数据目录中的临时文件存储目录
            from utils import get_app_data_dir
            app_data_dir = get_app_data_dir()
            temp_quota_dir = os.path.join(app_data_dir, "temp_quota_data")
            
            # 确保临时目录存在
            if not os.path.exists(temp_quota_dir):
                os.makedirs(temp_quota_dir)
                
            # 在临时目录中创建.env文件
            self.temp_env_file = os.path.join(temp_quota_dir, ".env")
            
            # 检查并删除已存在的.env文件
            if os.path.exists(self.temp_env_file):
                try:
                    os.remove(self.temp_env_file)
                    self.log_produced.emit("已删除已存在的临时配置文件", "info")
                except Exception as e:
                    self.log_produced.emit(f"删除已存在的临时配置文件失败: {str(e)}", "warning")
            
            # 从设置生成.env内容
            env_content = self.generate_env_content()
            
            # 写入临时.env文件
            with open(self.temp_env_file, 'w', encoding='utf-8') as f:
                f.write(env_content)
                
            self.log_produced.emit("环境准备完成", "info")
            self.progress_updated.emit(10, 100)
            return True
            
        except Exception as e:
            self.log_produced.emit(f"准备环境失败: {str(e)}", "error")
            return False
    
    def generate_env_content(self):
        """生成.env文件内容"""
        # 获取配置项
        email_type = self.settings.get("auto_register_email_type", "temp")
        domains_str = self.settings.get("auto_register_domain", "")
        
        # 解析域名字符串，支持多个域名（用逗号分隔）
        domains = [d.strip() for d in domains_str.split(",") if d.strip()]
        
        # 如果没有有效域名，使用空字符串
        if not domains:
            domain = ""
            self.log_produced.emit("警告：未配置有效域名", "warning")
        else:
            # 随机选择一个域名
            import random
            domain = random.choice(domains)
            
            if len(domains) > 1:
                self.log_produced.emit(f"从 {len(domains)} 个域名中随机选择: {domain}", "info")
        
        lines = [
            "# 自动生成的配置文件",
            "",
            f"# 域名配置",
            f"DOMAIN={domain}",
            ""
        ]
        
        # 浏览器类型配置
        browser_type = self.settings.get("auto_register_browser_type", "chrome")
        lines.append(f"# 浏览器类型配置")
        lines.append(f"BROWSER_TYPE='{browser_type}'")
        lines.append("")
        
        # 自定义Chrome路径配置
        if browser_type == "custom_chrome":
            custom_chrome_path = self.settings.get("auto_register_custom_chrome_path", "")
            if custom_chrome_path:
                lines.append(f"# 自定义Chrome浏览器路径")
                lines.append(f"BROWSER_CUSTOM_PATH='{custom_chrome_path}'")
                lines.append("")
        
        # 代理配置
        proxy = self.settings.get("auto_register_proxy", "")
        if proxy:
            lines.append(f"# 代理配置")
            lines.append(f"BROWSER_PROXY='{proxy}'")
            lines.append("")
        
        # 无头模式配置 - 始终写入无头模式配置，无论是True还是False
        headless = self.settings.get("auto_register_headless", False)
        lines.append(f"# 无头模式")
        lines.append(f"BROWSER_HEADLESS='{str(headless).lower()}'")
        lines.append("")
        
        # 邮箱配置
        if email_type == "temp":
            # 临时邮箱配置
            temp_mail = self.settings.get("auto_register_temp_mail", "")
            temp_mail_epin = self.settings.get("auto_register_temp_mail_epin", "")
            
            lines.append(f"# 临时邮箱配置")
            lines.append(f"TEMP_MAIL={temp_mail}")
            lines.append(f"TEMP_MAIL_EPIN={temp_mail_epin}")
            lines.append("")
            
        else:
            # IMAP邮箱配置
            imap_server = self.settings.get("auto_register_imap_server", "")
            imap_port = self.settings.get("auto_register_imap_port", "")
            imap_user = self.settings.get("auto_register_imap_user", "")
            imap_pass = self.settings.get("auto_register_imap_pass", "")
            
            lines.append(f"# IMAP邮箱配置")
            lines.append(f"IMAP_SERVER={imap_server}")
            lines.append(f"IMAP_PORT={imap_port}")
            lines.append(f"IMAP_USER={imap_user}")
            lines.append(f"IMAP_PASS={imap_pass}")
            lines.append("")
        
        # 验证码超时设置
        turnstile_timeout = self.settings.get("auto_register_turnstile_timeout", "20")
        verification_timeout = self.settings.get("auto_register_verification_timeout", "180")
        
        lines.append(f"# 验证超时设置")
        lines.append(f"TURNSTILE_TIMEOUT={turnstile_timeout}")
        lines.append(f"VERIFICATION_CODE_TIMEOUT={verification_timeout}")
        lines.append("")
        
        # 验证码配置
        verification_pattern = self.settings.get("auto_register_verification_pattern", "")
        if verification_pattern:
            lines.append(f"# 验证码匹配规则")
            lines.append(f"VERIFICATION_CODE_PATTERN='{verification_pattern}'")
            lines.append("")

        # 手动CF验证配置
        manual_cf = self.settings.get("auto_register_manual_cf", False)
        lines.append(f"# 手动CF人机验证")
        lines.append(f"MANUAL_CF={'True' if manual_cf else 'False'}")
        lines.append("")
            
        # 清理邮件配置 - 始终写入清理邮件配置，无论是True还是False
        clean_emails = self.settings.get("auto_register_clean_emails", False)
        lines.append(f"# 清理邮件配置")
        lines.append(f"CLEAN_ALL_CURSOR_MAILS='{str(clean_emails).lower()}'")
        lines.append("")
        
        # 显示验证码邮件内容设置
        show_email_content = self.settings.get("auto_register_show_email_content", False)
        lines.append(f"# 调试配置")
        lines.append(f"SHOW_VERIFICATION_EMAIL_CONTENT='{str(show_email_content).lower()}'")
        lines.append("")
        
        return "\n".join(lines)
    
    def execute_auto_register(self):
        """执行自动注册逻辑"""
        try:
            # 设置当前线程的属性，以便在sign_in_by_email_code中能够访问到信号和事件
            import threading
            current_thread = threading.current_thread()
            current_thread.manual_card_required = self.manual_card_required
            current_thread.manual_card_event = self.manual_card_event

            # 保存原始环境变量以供恢复
            self.original_env = dict(os.environ)
            self.log_produced.emit("开始执行自动注册流程...", "info")
            
            # 首先检查解释器状态
            if self._is_interpreter_shutting_down():
                self.log_produced.emit("Python解释器正在关闭，无法执行注册流程", "error")
                self.registration_state = "failed"
                self.failure_reason = "Python解释器正在关闭"
                return False
                
            # 不再需要添加cursor-auto目录到Python路径
            # sys.path.insert(0, self.auto_dir)
            
            # 导入必要模块
            self.progress_updated.emit(15, 100)
            self.log_produced.emit("导入模块...", "info")
            
            # 在导入模块前加载临时.env文件中的环境变量
            if self.temp_env_file and os.path.exists(self.temp_env_file):
                try:
                    # 导入dotenv模块
                    from dotenv import load_dotenv
                    # 加载临时.env文件
                    load_dotenv(self.temp_env_file, override=True)
                    # 重要：记录当前邮箱类型
                    email_type = self.settings.get("auto_register_email_type", "temp")
                    self.log_produced.emit(f"当前邮箱类型: {email_type}", "info")
                except Exception as e:
                    self.log_produced.emit(f"加载.env文件失败: {str(e)}", "error")
                    return False
            
            # 不再需要importlib
            # import importlib.util
            # import importlib
            
            # 检查停止标志
            if self._stop_flag:
                self.log_produced.emit("自动注册流程已被用户取消", "warning")
                self.registration_state = "failed"
                self.failure_reason = "用户取消"
                return False
                
            # 不再需要确保能找到模块
            # if self.auto_dir not in sys.path:
            #     sys.path.insert(0, self.auto_dir)
                
            # 不再需要切换工作目录
            # original_dir = os.getcwd()
            # os.chdir(self.auto_dir)
            
            # 保存原始print函数
            original_print = print
            # 导入builtins模块 - 在try块开始就导入，确保在异常处理时可用
            import builtins
            # 创建一个无操作的print函数用于替代
            def silent_print(*args, **kwargs):
                pass
            
            try:
                # 开始重定向标准输出和标准错误
                with self._redirect_stdout():
                    # 使用标准导入
                    self.log_produced.emit("初始化BrowserManager...", "info")
                    from core.cursor_auto import browser_utils
                    BrowserManager = browser_utils.BrowserManager
                    
                    # 处理重定向的输出
                    self._process_redirected_output()
                    
                    # 检查停止标志
                    if self._stop_flag:
                        self.log_produced.emit("自动注册流程已被用户取消", "warning")
                        self.registration_state = "failed"
                        self.failure_reason = "用户取消"
                        return False
                    
                    self.log_produced.emit("初始化EmailVerificationHandler...", "info")

                    # 根据邮箱类型显示更详细的初始化信息
                    email_type = self.settings.get("auto_register_email_type", "temp")
                    if email_type == "temp":
                        self.log_produced.emit("正在测试临时邮箱连接...", "info")
                    else:
                        self.log_produced.emit("正在测试IMAP邮箱连接...", "info")

                    from core.cursor_auto import get_email_code
                    EmailVerificationHandler = get_email_code.EmailVerificationHandler
                    # 新增：初始化时挂到self上，便于stop时终止
                    try:
                        self.email_handler = EmailVerificationHandler(email_type)
                        # 新增：传递终止标志
                        if hasattr(self.email_handler, 'set_should_stop'):
                            self.email_handler.set_should_stop(self._stop_flag)

                        # 连接成功后显示成功信息
                        if email_type == "temp":
                            self.log_produced.emit("临时邮箱连接测试成功", "success")
                        else:
                            self.log_produced.emit("IMAP邮箱连接测试成功", "success")
                    except ConnectionError as e:
                        # 捕获连接错误，显示简化的错误信息
                        error_message = str(e)

                        # 根据邮箱类型显示不同的错误信息
                        if email_type == "temp":
                            if "状态码" in error_message:
                                self.log_produced.emit("临时邮箱连接失败: 服务器响应异常，请检查网络连接", "error")
                            elif "超时" in error_message or "timeout" in error_message.lower():
                                self.log_produced.emit("临时邮箱连接超时: 网络连接不稳定，请检查网络或稍后重试", "error")
                            else:
                                self.log_produced.emit(f"临时邮箱连接失败: {error_message}", "error")
                        else:
                            # IMAP邮箱错误处理
                            if "QQ邮箱登录失败" in error_message:
                                self.log_produced.emit("IMAP邮箱连接失败: QQ邮箱登录失败，请检查密码或IMAP服务设置", "error")
                            elif "登录失败" in error_message:
                                self.log_produced.emit("IMAP邮箱连接失败: 登录失败，请检查账号密码", "error")
                            elif "超时" in error_message or "timeout" in error_message.lower():
                                self.log_produced.emit("IMAP邮箱连接超时: 网络连接不稳定，请检查网络或稍后重试", "error")
                            else:
                                self.log_produced.emit(f"IMAP邮箱连接失败: {error_message}", "error")
                        # self.log_produced.emit(simplified_error, "error")
                        
                        # 静默保存错误日志，不显示保存信息
                        email_type = self.settings.get("auto_register_email_type", "temp")
                        log_filename = "imap_connection_error.log" if email_type == "imap" else "temp_mail_connection_error.log"
                        self._save_error_to_log(log_filename, error_message)
                        
                        # 跳过显示详细提示，简化输出
                        
                        # 终止注册流程，不抛出新异常
                        builtins.print = original_print  # 恢复print函数
                        self._process_redirected_output()
                        return False
                    except Exception as e:
                        # 捕获其他可能的异常
                        error_message = f"初始化邮箱验证处理器失败: {str(e)}"
                        self.log_produced.emit(error_message, "error")
                        self._save_error_to_log("email_handler_init_error.log", error_message)
                        self._process_redirected_output()
                        builtins.print = original_print  # 恢复print函数
                        self.registration_state = "failed"
                        self.failure_reason = f"初始化邮箱验证失败: {str(e)}"
                        return False
                    
                    # 拦截和替换 get_email_code 模块中的日志函数
                    original_print_box = get_email_code.print_box
                    original_log_info = get_email_code.log_info
                    original_log_warn = get_email_code.log_warn
                    original_log_error = get_email_code.log_error
                    original_log_process = get_email_code.log_process
                    original_log_success = get_email_code.log_success
                    
                    # 替换为我们自己的函数，用于捕获输出
                    def custom_print_box(title="", content=None, footer=None):
                        # 让原始函数执行以保持控制台输出
                        original_print_box(title, content, footer)
                        
                        # 特别处理邮件内容显示
                        if "验证码邮件内容" in title:
                            try:
                                # 发送标题
                                self.log_produced.emit(f"{title}", "info")
                                
                                # 处理内容数组
                                if content and isinstance(content, list):
                                    for item in content:
                                        if isinstance(item, tuple) and len(item) >= 2:
                                            # 获取内容文本
                                            text = item[1]
                                            # 特殊处理发件人、主题、邮件正文等关键内容
                                            if "发件人:" in text or "主题:" in text:
                                                self.log_produced.emit(f"  {text}", "info")
                                            elif "邮件正文:" in text:
                                                self.log_produced.emit(f"  {text}", "info")
                                            elif item[0] == Colors.CYAN:  # 邮件正文通常使用CYAN颜色
                                                # 对长文本邮件内容进行包装，确保所有内容都被显示
                                                lines = text.split('\n')
                                                for line in lines:
                                                    if line.strip():
                                                        self.log_produced.emit(f"    {line}", "info")
                                            else:
                                                self.log_produced.emit(f"  {text}", "info")
                            except Exception as e:
                                self.log_produced.emit(f"处理邮件内容时出错: {str(e)}", "error")
                        # 处理其他正常输出
                        else:
                            # 处理重定向的输出
                            self._process_redirected_output()
                    
                    def custom_log_info(msg, show_box=False):
                        # 让原始函数执行以保持控制台输出
                        original_log_info(msg, show_box)
                        
                        # 去掉日志前缀
                        stripped_msg = self._strip_log_prefix(msg)
                        
                        # 只对高频且非重要消息进行过滤
                        is_high_frequency = "等待验证码邮件中" in msg
                        
                        if is_high_frequency:
                            # 控制高频消息的显示频率，每10秒只显示一次
                            if int(time.time()) % 10 != 0:
                                return
                            
                        # 检查是否包含关键状态信息，如果有，设置状态
                        # 注意：仅当明确的成功信息出现时才设置成功状态
                        # 不再通过日志消息自动设置成功状态，防止误报
                        if ("注册成功" in msg and not "开始" in msg) or "账号数据保存成功" in msg:
                            # 只有当执行到明确的最终成功步骤时才设置状态
                            if "成功创建账号" in msg or "账号数据保存成功" in msg:
                                self.registration_state = "success"
                                self.log_produced.emit(f"[状态变更] 设置注册状态为成功: {stripped_msg}", "success")
                        elif "注册失败" in msg or "验证码获取失败" in msg or "临时邮箱连接测试出错" in msg:
                            self.registration_state = "failed"
                            self.failure_reason = stripped_msg
                            self.log_produced.emit(f"[状态变更] 设置注册状态为失败: {stripped_msg}", "error")
                        
                        # 发送日志信号
                        self.log_produced.emit(stripped_msg, "info")
                        
                        # 处理重定向的输出
                        self._process_redirected_output()
                    
                    def custom_log_warn(msg, show_box=False):
                        original_log_warn(msg, show_box)
                        # 警告信息通常都很重要，不进行过滤
                        stripped_msg = self._strip_log_prefix(msg)
                        self.log_produced.emit(stripped_msg, "warning")
                        self._process_redirected_output()
                    
                    def custom_log_error(msg, show_box=False):
                        original_log_error(msg, show_box)
                        stripped_msg = self._strip_log_prefix(msg)
                        self.log_produced.emit(stripped_msg, "error")
                        
                        # 设置失败状态，除非已经明确设置为成功
                        if self.registration_state != "success" and any(keyword in msg for keyword in 
                              ["验证码获取失败", "注册失败", "验证码超时", "浏览器任务失败",
                              "无法获取验证码", "登录失败", "无法连接", "连接失败", "IMAP错误", 
                              "授权流程失败", "邮箱连接测试失败", "认证失败", "邮件格式错误", 
                              "验证码发送失败", "临时邮箱连接测试出错", "Max retries exceeded", 
                              "ProxyError", "Unable to connect to proxy"]):
                            self.registration_state = "failed"
                            self.failure_reason = stripped_msg
                            self.log_produced.emit(f"[状态变更] 设置注册状态为失败，原因: {stripped_msg}", "error")
                        
                        self._process_redirected_output()
                    
                    def custom_log_process(msg, show_box=False):
                        original_log_process(msg, show_box)
                        
                        # 去掉日志前缀
                        stripped_msg = self._strip_log_prefix(msg)
                        
                        # 对于进度消息，只对少数类型进行过滤
                        is_frequent_progress = any(keyword in msg for keyword in 
                                               ["等待页面刷新", "等待页面加载", "暂停", "等待元素出现",
                                                "延迟", "等待验证码", "尝试获取邮件"])
                        
                        if is_frequent_progress:
                            # 控制显示频率
                            current_time = int(time.time())
                            if current_time % 5 != 0:  # 每5秒显示一次
                                return
                        
                        # 发送日志信号
                        self.log_produced.emit(stripped_msg, "process")
                        
                        # 处理重定向的输出
                        self._process_redirected_output()
                    
                    def custom_log_success(msg, show_box=False):
                        original_log_success(msg, show_box)
                        stripped_msg = self._strip_log_prefix(msg)
                        self.log_produced.emit(stripped_msg, "success")
                        
                        # 同时设置状态，更全面地捕获成功情况
                        if any(keyword in msg for keyword in 
                              ["注册成功", "成功创建账号", "验证码登录成功", "账号数据保存成功", 
                               "账号信息已成功保存", "成功获取token", "accessToken获取成功",
                               "OAuth流程成功完成", "成功登录Cursor账号", "账号已生成",
                               "帐户已注册", "账号创建成功", "邮箱验证已完成"]):
                            self.registration_state = "success"
                            # 记录状态变更
                            self.log_produced.emit(f"[状态变更] 设置注册状态为成功: {stripped_msg}", "success")
                        
                        self._process_redirected_output()
                    
                    # 应用替换
                    get_email_code.print_box = custom_print_box
                    get_email_code.log_info = custom_log_info
                    get_email_code.log_warn = custom_log_warn
                    get_email_code.log_error = custom_log_error
                    get_email_code.log_process = custom_log_process
                    get_email_code.log_success = custom_log_success
                    
                    # 处理重定向的输出
                    self._process_redirected_output()
                    
                    self.log_produced.emit("初始化EmailGenerator...", "info")
                    # 使用标准导入
                    from core.cursor_auto import cursor_pro_keep_alive
                    EmailGenerator = cursor_pro_keep_alive.EmailGenerator
                    AccountInfo = cursor_pro_keep_alive.AccountInfo
                    sign_up_account = cursor_pro_keep_alive.sign_up_account
                    handle_auth_flow = cursor_pro_keep_alive.handle_auth_flow
                    save_account_to_json = cursor_pro_keep_alive.save_account_to_json
                    
                    # 保存原始update_progress函数
                    original_update_progress = cursor_pro_keep_alive.update_progress
                    
                    # 创建一个永远不打印到控制台的update_progress函数
                    def silent_update_progress(current, total=100, print_to_console=False):
                        original_update_progress(current, total, print_to_console=False)
                    
                    # 替换更新进度函数，强制不打印到控制台
                    cursor_pro_keep_alive.update_progress = silent_update_progress
                    
                    # 处理重定向的输出
                    self._process_redirected_output()
                    
                    # 在验证码阶段完全禁用print函数，防止进度信息打印
                    builtins = importlib.import_module("builtins")
                    builtins.print = silent_print
                    
                    # 检查停止标志和线程状态 - 在初始化浏览器前检查
                    if self._stop_flag or threading.current_thread()._is_stopped:
                        builtins.print = original_print  # 恢复print函数
                        self.log_produced.emit("自动注册流程已被取消或线程已停止", "warning")
                        return False
                    
                    # 初始化组件
                    cursor_pro_keep_alive.update_progress(25, 100, print_to_console=False)
                    self.log_produced.emit("初始化浏览器管理器...", "info")
                    
                    try:
                        self.browser_manager = BrowserManager()
                    except RuntimeError as e:
                        # 特别捕获"can't create new thread at interpreter shutdown"错误
                        if "can't create new thread" in str(e) and "interpreter shutdown" in str(e):
                            self.log_produced.emit("程序正在关闭，无法创建浏览器线程", "error")
                            self.registration_state = "failed"
                            self.failure_reason = "程序正在关闭，无法创建浏览器线程"
                            return False
                        raise  # 重新抛出其他运行时错误
                    
                    # 处理重定向的输出
                    self._process_redirected_output()
                    
                    # 检查停止标志
                    if self._stop_flag:
                        builtins.print = original_print  # 恢复print函数
                        self.log_produced.emit("自动注册流程已被用户取消", "warning")
                        self.registration_state = "failed"
                        self.failure_reason = "用户取消"
                        return False
                    
                    cursor_pro_keep_alive.update_progress(30, 100, print_to_console=False)
                    self.log_produced.emit("初始化浏览器...", "info")
                    
                    try:
                        self.browser = self.browser_manager.init_browser()
                    except RuntimeError as e:
                        # 捕获浏览器初始化过程中的线程错误
                        if "can't create new thread" in str(e):
                            self.log_produced.emit("无法创建浏览器线程，程序可能正在关闭", "error")
                            self.registration_state = "failed"
                            self.failure_reason = "无法创建浏览器线程，程序可能正在关闭"
                            return False
                        raise  # 重新抛出其他运行时错误
                    
                    # 检查停止标志
                    if self._stop_flag:
                        builtins.print = original_print  # 恢复print函数
                        self.log_produced.emit("自动注册流程已被用户取消", "warning")
                        self.registration_state = "failed"
                        self.failure_reason = "用户取消"
                        return False
                    
                    cursor_pro_keep_alive.update_progress(35, 100, print_to_console=False)
                    self.log_produced.emit("初始化邮箱验证处理器...", "info")
                    
                    # 获取当前设置的邮箱类型
                    email_type = self.settings.get("auto_register_email_type", "temp")
                    
                    # 根据邮箱类型选择不同的初始化方式
                    if email_type == "imap":
                        self.log_produced.emit("使用IMAP邮箱模式...", "info")
                        
                        # IMAP配置
                        imap_server = self.settings.get("auto_register_imap_server", "")
                        imap_port = self.settings.get("auto_register_imap_port", "")
                        imap_user = self.settings.get("auto_register_imap_user", "")
                        imap_pass = self.settings.get("auto_register_imap_pass", "")
                        
                        # 检查IMAP配置完整性
                        if not imap_server or not imap_port or not imap_user or not imap_pass:
                            self.log_produced.emit("IMAP邮箱配置不完整，无法继续", "error")
                            self.registration_state = "failed"
                            self.failure_reason = "IMAP邮箱配置不完整，无法继续"
                            return False
                            
                        # 从get_email_code模块获取测试IMAP方法
                        test_imap_func = getattr(get_email_code, "test_imap_connection", None)
                        if test_imap_func:
                            self.log_produced.emit("使用cursor-auto模块测试IMAP连接...", "info")
                            try:
                                success, message = test_imap_func(imap_server, imap_port, imap_user, imap_pass)
                                if success:
                                    self.log_produced.emit(f"IMAP连接测试成功", "success")
                                else:
                                    # 更好地显示错误消息
                                    self.log_produced.emit(f"IMAP连接测试失败", "error")
                                    # 添加一个更明确的自定义错误消息，确保用户知道问题所在
                                    self.log_produced.emit(f"无法连接到IMAP邮箱，自动注册将无法继续", "error")
                                    # 分段显示可能有多行的消息
                                    if message:
                                        for line in message.split('\n'):
                                            if line.strip():
                                                # 根据行内容设置日志类型
                                                if "解决方法" in line or "方法" in line:
                                                    self.log_produced.emit(line, "info")
                                                elif "1." in line or "2." in line or "3." in line:
                                                    self.log_produced.emit(line, "info")
                                                else:
                                                    self.log_produced.emit(line, "error")
                                    # 保存错误信息到临时文件，以便在清理后仍能查看
                                    self._save_error_to_log("imap_connection_error.log", message)
                                    self.registration_state = "failed"
                                    self.failure_reason = f"IMAP连接测试失败: {message}"
                                    return False
                            except Exception as e:
                                error_msg = f"IMAP连接测试异常: {str(e)}"
                                self.log_produced.emit(error_msg, "error")
                                self._save_error_to_log("imap_connection_error.log", error_msg)
                                self.registration_state = "failed"
                                self.failure_reason = f"IMAP连接测试异常: {error_msg}"
                                return False
                    else:
                        self.log_produced.emit("使用临时邮箱模式...", "info")
                        
                        # 临时邮箱配置
                        temp_mail = self.settings.get("auto_register_temp_mail", "")
                        temp_mail_epin = self.settings.get("auto_register_temp_mail_epin", "")
                        
                        # 检查临时邮箱配置完整性
                        if not temp_mail or not temp_mail_epin:
                            self.log_produced.emit("临时邮箱配置不完整，无法继续", "error")
                            self.registration_state = "failed"
                            self.failure_reason = "临时邮箱配置不完整，无法继续"
                            return False
                            
                        # 从get_email_code模块获取测试临时邮箱方法
                        test_temp_mail_func = getattr(get_email_code, "test_temp_mail", None)
                        if test_temp_mail_func:
                            self.log_produced.emit("使用cursor-auto模块测试临时邮箱连接...", "info")
                            try:
                                success, message = test_temp_mail_func(temp_mail, temp_mail_epin)
                                if success:
                                    self.log_produced.emit(f"临时邮箱连接测试成功", "success")
                                else:
                                    # 更好地显示错误消息
                                    self.log_produced.emit(f"临时邮箱连接测试失败", "error")
                                    # 添加一个更明确的自定义错误消息，确保用户知道问题所在
                                    self.log_produced.emit(f"无法连接到临时邮箱，自动注册将无法继续", "error")
                                    # 分段显示可能有多行的消息
                                    if message:
                                        for line in message.split('\n'):
                                            if line.strip():
                                                # 根据行内容设置日志类型
                                                if "解决方法" in line or "方法" in line:
                                                    self.log_produced.emit(line, "info")
                                                elif "1." in line or "2." in line or "3." in line:
                                                    self.log_produced.emit(line, "info")
                                                else:
                                                    self.log_produced.emit(line, "error")
                                    # 保存错误信息到临时文件，以便在清理后仍能查看
                                    self._save_error_to_log("temp_mail_connection_error.log", message)
                                    self.registration_state = "failed"
                                    self.failure_reason = f"临时邮箱连接测试失败: {message}"
                                    return False
                            except Exception as e:
                                error_msg = f"临时邮箱连接测试异常: {str(e)}"
                                self.log_produced.emit(error_msg, "error")
                                self._save_error_to_log("temp_mail_connection_error.log", error_msg)
                                self.registration_state = "failed"
                                self.failure_reason = f"临时邮箱连接测试异常: {error_msg}"
                                return False
                    
                    # 创建EmailVerificationHandler时明确传递邮箱类型
                    email_handler = EmailVerificationHandler(email_type=email_type)
                    
                    # 处理重定向的输出
                    self._process_redirected_output()
                    
                    # 填充全局变量
                    cursor_pro_keep_alive.email_handler = email_handler
                    cursor_pro_keep_alive.account_info = AccountInfo()
                    cursor_pro_keep_alive.worker_instance = self  # 设置工作线程实例
                    
                    # 设置日志和进度回调
                    cursor_pro_keep_alive.set_log_callback(
                        lambda msg, log_type: self._emit_log_with_deduplication(msg, log_type)
                    )
                    cursor_pro_keep_alive.set_progress_callback(
                        lambda current, total: self.progress_updated.emit(current, total)
                    )
                    
                    # 处理重定向的输出
                    self._process_redirected_output()
                    
                    # 检查停止标志
                    if self._stop_flag:
                        builtins.print = original_print  # 恢复print函数
                        self.log_produced.emit("自动注册流程已被用户取消", "warning")
                        self.registration_state = "failed"
                        self.failure_reason = "用户取消"
                        return False
                    
                    # 生成随机账号信息
                    cursor_pro_keep_alive.update_progress(40, 100, print_to_console=False)
                    self.log_produced.emit("生成随机账号信息...", "info")
                    email_generator = EmailGenerator()
                    account_info = email_generator.get_account_info()
                    
                    # 处理重定向的输出
                    self._process_redirected_output()
                    
                    # 账号信息检查
                    if not account_info or "email" not in account_info:
                        builtins.print = original_print  # 恢复print函数
                        self.log_produced.emit("生成账号信息失败: 邮箱信息不完整", "error")
                        self.registration_state = "failed"
                        self.failure_reason = "生成账号信息失败: 邮箱信息不完整"
                        return False
                    
                    # 账号信息
                    account = account_info["email"]
                    password = account_info["password"]
                    cursor_pro_keep_alive.first_name = account_info["first_name"]
                    cursor_pro_keep_alive.last_name = account_info["last_name"]
                    # 设置为全局变量，供sign_up_account函数使用
                    cursor_pro_keep_alive.account = account
                    cursor_pro_keep_alive.password = password
                    
                    self.log_produced.emit(f"生成的邮箱账号: {account}", "info")
                    
                    # 处理重定向的输出
                    self._process_redirected_output()
                    
                    # 获取标签页
                    initial_tab = self.browser.get_tab()
                    
                    # 检查浏览器类型
                    browser_type = self.settings.get("auto_register_browser_type", "chrome")
                    
                    if browser_type == 'edge':
                        self.log_produced.emit("检测到Edge浏览器，尝试在新标签页中执行操作...", "info")
                        try:
                            # 创建新标签页
                            tab = self.browser.new_tab()
                            # 关闭原始标签页
                            initial_tab.close()
                            self.log_produced.emit("已创建新标签页并关闭原始标签页", "info")
                        except Exception as e:
                            self.log_produced.emit(f"处理Edge标签页时出错: {e}，将尝试在原始标签页继续", "warning")
                            tab = initial_tab # 出错时回退到原始标签页
                    else:
                        # 对于非Edge浏览器，继续使用原始标签页
                        tab = initial_tab
                        
                    # 重置 Turnstile (在最终使用的标签页上执行)
                    tab.run_js("try { turnstile.reset() } catch(e) { }", timeout=5)

                    # 记录当前使用的注册方案
                    method_name = "方案一(邮箱验证码登录)" if self.register_method == 1 else "方案二(账号注册)"
                    self.log_produced.emit(f"使用{method_name}进行注册", "info")
                    
                    # 根据选择的方案执行不同的注册流程
                    if self.register_method == 1:
                        # 方案一: 使用邮箱验证码直接登录
                        self.log_produced.emit("开始邮箱验证码登录流程...", "info")
                        
                        # 处理重定向的输出
                        self._process_redirected_output()
                        
                        # 使用邮箱验证码登录
                        token_data = cursor_pro_keep_alive.sign_in_by_email_code(self.browser, tab, account, password)
                        
                        # 处理重定向的输出
                        self._process_redirected_output()
                        
                        if token_data and token_data.get('accessToken') and token_data.get('refreshToken'):
                            self.log_produced.emit("登录授权成功，正在保存账号信息...", "info")
                            
                            # 保存账号信息到应用的JSON文件中
                            self.log_produced.emit("保存账号信息...", "info")
                            
                            # 使用相同的保存逻辑
                            # 获取应用使用的cursor_accounts.json路径
                            account_data = AccountData()
                            application_path = os.path.dirname(account_data.accounts_file)
                            app_accounts_file = os.path.join(application_path, "cursor_accounts.json")
                            
                            # 确保目录存在
                            os.makedirs(os.path.dirname(app_accounts_file), exist_ok=True)
                            
                            # 修改save_account_to_json函数中的路径
                            cursor_pro_keep_alive.cursor_accounts_file = app_accounts_file
                            
                            # 记录当前时间，用于验证文件是否被更新
                            file_existed_before = os.path.exists(app_accounts_file)
                            file_time_before = os.path.getmtime(app_accounts_file) if file_existed_before else 0
                            account_data_before = []
                            if file_existed_before:
                                try:
                                    with open(app_accounts_file, 'r', encoding='utf-8') as f:
                                        account_data_before = json.load(f)
                                except:
                                    account_data_before = []
                            
                            # 保存账号信息
                            save_result = cursor_pro_keep_alive.save_account_to_json(account, password, token_data['accessToken'], self.register_method)
                            
                            # 验证文件是否真的被保存
                            verification_passed = False
                            
                            # 立即验证保存是否成功
                            if save_result:
                                # 检查文件是否存在
                                if os.path.exists(app_accounts_file):
                                    # 如果是新创建的文件或文件有更新
                                    if not file_existed_before or os.path.getmtime(app_accounts_file) > file_time_before:
                                        try:
                                            # 读取文件验证账号信息是否存在
                                            with open(app_accounts_file, 'r', encoding='utf-8') as f:
                                                saved_accounts = json.load(f)
                                                # 检查账号是否在保存的数据中
                                                for acc in saved_accounts:
                                                    if acc.get('email') == account:
                                                        verification_passed = True
                                                        break
                                        except Exception as e:
                                            self.log_produced.emit(f"验证账号文件失败: {str(e)}", "error")
                                    else:
                                        # 文件存在但未被修改
                                        # 检查是否原来就有该账号
                                        for acc in account_data_before:
                                            if acc.get('email') == account:
                                                verification_passed = True
                                                break
                                        
                                        if not verification_passed:
                                            self.log_produced.emit("文件未被更新，保存可能失败", "warning")
                                else:
                                    self.log_produced.emit(f"保存账号失败: 文件 {app_accounts_file} 不存在", "error")
                            
                            # 根据验证结果显示保存结果
                            if verification_passed:
                                self.log_produced.emit(f"账号信息已成功保存到: {app_accounts_file}", "info")
                            else:
                                # 如果验证失败，尝试手动保存
                                try:
                                    # 创建备份文件夹
                                    backup_dir = os.path.join(get_app_data_dir(), "account_backups")
                                    os.makedirs(backup_dir, exist_ok=True)
                                    
                                    # 直接创建账号文件
                                    backup_file = os.path.join(backup_dir, "cursor_accounts_backup.json")
                                    account_entry = {
                                        "email": account,
                                        "password": "更快自动注册方案没有密码可保存" if self.register_method == 1 else password,
                                        "token": token_data['accessToken'],
                                        "refresh_token": token_data.get('refreshToken', ''),
                                        "created": time.strftime("%Y-%m-%d %H:%M:%S")
                                    }
                                    
                                    # 读取现有备份数据
                                    backup_data = []
                                    if os.path.exists(backup_file):
                                        try:
                                            with open(backup_file, 'r', encoding='utf-8') as f:
                                                backup_data = json.load(f)
                                        except:
                                            backup_data = []
                                    
                                    # 添加新账号
                                    backup_data.append(account_entry)
                                    
                                    # 写入备份文件
                                    with open(backup_file, 'w', encoding='utf-8') as f:
                                        json.dump(backup_data, f, indent=2)
                                    
                                    self.log_produced.emit(f"原始保存失败，已创建备份: {backup_file}", "warning")
                                except Exception as e:
                                    self.log_produced.emit(f"创建备份文件失败: {str(e)}", "error")
                                
                                self.log_produced.emit(f"无法验证账号信息是否成功保存", "warning")
                            
                            # 根据仅注册标志决定是否更新应用认证信息
                            if self.register_only:
                                self.log_produced.emit("仅注册模式：跳过更新应用认证信息", "info")
                            else:
                                # 关闭Cursor进程
                                self.log_produced.emit("正在关闭Cursor进程...", "info")
                                self.cursor_killed = self.kill_cursor_process()
                                
                                if self.cursor_killed:
                                    self.log_produced.emit("成功关闭Cursor进程", "success")
                                else:
                                    self.log_produced.emit("未能找到或关闭Cursor进程，可能会影响更新效果", "warning")
                                
                                # 更新授权信息
                                self.log_produced.emit("正在更新应用认证信息...", "info")
                                cursor_auth_module = importlib.import_module("core.cursor_auto.cursor_auth_manager")
                                CursorAuthManager = cursor_auth_module.CursorAuthManager
                                auth_manager = CursorAuthManager()
                                auth_result = auth_manager.update_auth(
                                    email=account,
                                    access_token=token_data['accessToken'],
                                    refresh_token=token_data['refreshToken']
                                )
                                
                                # 处理重定向的输出
                                self._process_redirected_output()
                                
                                if auth_result:
                                    self.log_produced.emit("认证信息更新成功", "success")
                                    
                                    # 如果之前关闭了Cursor，根据设置决定是否重新启动它
                                    if self.cursor_killed:
                                        # 检查用户设置中是否开启了"关闭Cursor后自动启动"选项
                                        auto_restart_setting = True  # 默认为True
                                        try:
                                            settings_file = os.path.join(get_app_data_dir(), "settings.json")
                                            if os.path.exists(settings_file):
                                                with open(settings_file, 'r', encoding='utf-8') as f:
                                                    settings = json.load(f)
                                                    auto_restart_setting = settings.get("auto_restart_cursor", True)
                                        except Exception as e:
                                            self.log_produced.emit(f"读取设置时出错: {str(e)}", "warning")
                                        
                                        if auto_restart_setting:
                                            self.log_produced.emit("正在重新启动Cursor...", "info")
                                            if self.start_cursor_app():
                                                self.log_produced.emit("Cursor已成功重新启动", "success")
                                            else:
                                                self.log_produced.emit("Cursor自动重启失败，请手动启动Cursor", "warning")
                                        else:
                                            self.log_produced.emit("根据设置未自动启动Cursor，请手动启动", "info")
                                else:
                                    self.log_produced.emit("认证信息更新失败", "error")
                            if save_result:
                                # 检查文件是否存在
                                if os.path.exists(app_accounts_file):
                                    # 如果是新创建的文件或文件有更新
                                    if not file_existed_before or os.path.getmtime(app_accounts_file) > file_time_before:
                                        try:
                                            # 读取文件验证账号信息是否存在
                                            with open(app_accounts_file, 'r', encoding='utf-8') as f:
                                                saved_accounts = json.load(f)
                                                # 检查账号是否在保存的数据中
                                                for acc in saved_accounts:
                                                    if acc.get('email') == account:
                                                        verification_passed = True
                                                        break
                                        except Exception as e:
                                            self.log_produced.emit(f"验证账号文件失败: {str(e)}", "error")
                                    else:
                                        # 文件存在但未被修改
                                        # 检查是否原来就有该账号
                                        for acc in account_data_before:
                                            if acc.get('email') == account:
                                                verification_passed = True
                                                break
                                        
                                        if not verification_passed:
                                            self.log_produced.emit("文件未被更新，保存可能失败", "warning")
                                else:
                                    self.log_produced.emit(f"保存账号失败: 文件 {app_accounts_file} 不存在", "error")
                            
                            # 第八点五阶段：取消订阅处理
                            cancel_subscription_result = self._handle_subscription_cancellation(token_data['accessToken'])

                            # 根据验证结果处理
                            # 最终处理重定向的输出
                            self._process_redirected_output()
                            builtins.print = original_print  # 恢复print函数
                            self.registration_state = "success"
                            self.cancel_subscription_result = cancel_subscription_result  # 保存取消订阅结果
                            return True
                        else:
                            self.log_produced.emit("自动注册失败，请查看日志", "error")
                            # 最终处理重定向的输出
                            self._process_redirected_output()
                            builtins.print = original_print  # 恢复print函数
                            self.registration_state = "failed"
                            self.failure_reason = "自动注册失败，请查看日志"
                            return False
                    else:
                        # 方案二: 原有的账号注册流程
                        self.log_produced.emit("开始注册流程...", "info")
                        
                        # 处理重定向的输出
                        self._process_redirected_output()
                        
                        # 注册账号
                        if cursor_pro_keep_alive.sign_up_account(self.browser, tab):
                            # 处理重定向的输出
                            self._process_redirected_output()
                            
                            self.log_produced.emit("账号注册成功，开始授权流程...", "info")
                            
                            # 开始授权流程
                            token_data = cursor_pro_keep_alive.handle_auth_flow(self.browser, tab, account, password)
                            
                            # 处理重定向的输出
                            self._process_redirected_output()
                            
                            if token_data and token_data.get('accessToken') and token_data.get('refreshToken'):
                                self.log_produced.emit("授权流程成功，正在保存账号信息...", "info")
                                
                                # 保存账号信息到应用的JSON文件中
                                self.log_produced.emit("保存账号信息...", "info")
                                
                                # 获取应用使用的cursor_accounts.json路径
                                account_data = AccountData()
                                application_path = os.path.dirname(account_data.accounts_file)
                                app_accounts_file = os.path.join(application_path, "cursor_accounts.json")
                                
                                # 确保目录存在
                                os.makedirs(os.path.dirname(app_accounts_file), exist_ok=True)
                                
                                # 修改save_account_to_json函数中的路径
                                cursor_pro_keep_alive.cursor_accounts_file = app_accounts_file
                                
                                # 记录当前时间，用于验证文件是否被更新
                                file_existed_before = os.path.exists(app_accounts_file)
                                file_time_before = os.path.getmtime(app_accounts_file) if file_existed_before else 0
                                account_data_before = []
                                if file_existed_before:
                                    try:
                                        with open(app_accounts_file, 'r', encoding='utf-8') as f:
                                            account_data_before = json.load(f)
                                    except:
                                        account_data_before = []
                                
                                # 保存账号信息
                                save_result = cursor_pro_keep_alive.save_account_to_json(account, password, token_data['accessToken'], self.register_method)
                                
                                # 验证文件是否真的被保存
                                verification_passed = False
                                
                                # 立即验证保存是否成功
                                if save_result:
                                    # 检查文件是否存在
                                    if os.path.exists(app_accounts_file):
                                        # 如果是新创建的文件或文件有更新
                                        if not file_existed_before or os.path.getmtime(app_accounts_file) > file_time_before:
                                            try:
                                                # 读取文件验证账号信息是否存在
                                                with open(app_accounts_file, 'r', encoding='utf-8') as f:
                                                    saved_accounts = json.load(f)
                                                    # 检查账号是否在保存的数据中
                                                    for acc in saved_accounts:
                                                        if acc.get('email') == account:
                                                            verification_passed = True
                                                            break
                                            except Exception as e:
                                                self.log_produced.emit(f"验证账号文件失败: {str(e)}", "error")
                                        else:
                                            # 文件存在但未被修改
                                            # 检查是否原来就有该账号
                                            for acc in account_data_before:
                                                if acc.get('email') == account:
                                                    verification_passed = True
                                                    break
                                            
                                            if not verification_passed:
                                                self.log_produced.emit("文件未被更新，保存可能失败", "warning")
                                    else:
                                        self.log_produced.emit(f"保存账号失败: 文件 {app_accounts_file} 不存在", "error")
                                
                                # 根据验证结果显示保存结果
                                if verification_passed:
                                    self.log_produced.emit(f"账号信息已成功保存到: {app_accounts_file}", "info")
                                    
                                    # 新增：强制重新加载账户数据，确保新注册的账户在内存中
                                    try:
                                        # 获取主窗口实例
                                        from PySide6.QtWidgets import QApplication
                                        main_window = None
                                        for widget in QApplication.instance().topLevelWidgets():
                                            if hasattr(widget, 'account_data'):
                                                main_window = widget
                                                break
                                        
                                        # 如果找到主窗口，更新其账户数据
                                        if main_window and hasattr(main_window, 'account_data'):
                                            self.log_produced.emit("更新主窗口账户数据...", "info")
                                            # 重新加载账户文件数据到内存
                                            main_window.account_data.accounts = main_window.account_data.load_accounts()
                                            self.log_produced.emit("主窗口账户数据更新成功", "info")
                                        else:
                                            self.log_produced.emit("未找到主窗口或主窗口无账户数据对象", "warning")
                                    except Exception as e:
                                        self.log_produced.emit(f"更新主窗口账户数据时出错: {str(e)}", "warning")
                                else:
                                    # 如果验证失败，尝试手动保存
                                    try:
                                        # 创建备份文件夹
                                        backup_dir = os.path.join(get_app_data_dir(), "account_backups")
                                        os.makedirs(backup_dir, exist_ok=True)
                                        
                                        # 直接创建账号文件
                                        backup_file = os.path.join(backup_dir, "cursor_accounts_backup.json")
                                        account_entry = {
                                            "email": account,
                                            "password": "更快自动注册方案没有密码可保存" if self.register_method == 1 else password,
                                            "token": token_data['accessToken'],
                                            "refresh_token": token_data.get('refreshToken', ''),
                                            "created": time.strftime("%Y-%m-%d %H:%M:%S")
                                        }
                                        
                                        # 读取现有备份数据
                                        backup_data = []
                                        if os.path.exists(backup_file):
                                            try:
                                                with open(backup_file, 'r', encoding='utf-8') as f:
                                                    backup_data = json.load(f)
                                            except:
                                                backup_data = []
                                        
                                        # 添加新账号
                                        backup_data.append(account_entry)
                                        
                                        # 写入备份文件
                                        with open(backup_file, 'w', encoding='utf-8') as f:
                                            json.dump(backup_data, f, indent=2)
                                        
                                        self.log_produced.emit(f"原始保存失败，已创建备份: {backup_file}", "warning")
                                    except Exception as e:
                                        self.log_produced.emit(f"创建备份文件失败: {str(e)}", "error")
                                    
                                    self.log_produced.emit(f"无法验证账号信息是否成功保存", "warning")
                                
                                # 根据仅注册标志决定是否更新应用认证信息
                                if self.register_only:
                                    self.log_produced.emit("仅注册模式：跳过更新应用认证信息", "info")
                                else:
                                    # 关闭Cursor进程
                                    self.log_produced.emit("正在关闭Cursor进程...", "info")
                                    self.cursor_killed = self.kill_cursor_process()
                                    
                                    if self.cursor_killed:
                                        self.log_produced.emit("成功关闭Cursor进程", "success")
                                    else:
                                        self.log_produced.emit("未能找到或关闭Cursor进程，可能会影响更新效果", "warning")
                                    
                                    # 更新授权信息
                                    self.log_produced.emit("正在更新应用认证信息...", "info")
                                    cursor_auth_module = importlib.import_module("core.cursor_auto.cursor_auth_manager")
                                    CursorAuthManager = cursor_auth_module.CursorAuthManager
                                    auth_manager = CursorAuthManager()
                                    auth_result = auth_manager.update_auth(
                                        email=account,
                                        access_token=token_data['accessToken'],
                                        refresh_token=token_data['refreshToken']
                                    )
                                    
                                    # 处理重定向的输出
                                    self._process_redirected_output()
                                    
                                    if auth_result:
                                        self.log_produced.emit("认证信息更新成功", "success")
                                        
                                        # 如果之前关闭了Cursor，根据设置决定是否重新启动它
                                        if self.cursor_killed:
                                            # 检查用户设置中是否开启了"关闭Cursor后自动启动"选项
                                            auto_restart_setting = True  # 默认为True
                                            try:
                                                settings_file = os.path.join(get_app_data_dir(), "settings.json")
                                                if os.path.exists(settings_file):
                                                    with open(settings_file, 'r', encoding='utf-8') as f:
                                                        settings = json.load(f)
                                                        auto_restart_setting = settings.get("auto_restart_cursor", True)
                                            except Exception as e:
                                                self.log_produced.emit(f"读取设置时出错: {str(e)}", "warning")
                                            
                                            if auto_restart_setting:
                                                self.log_produced.emit("正在重新启动Cursor...", "info")
                                                if self.start_cursor_app():
                                                    self.log_produced.emit("Cursor已成功重新启动", "success")
                                                else:
                                                    self.log_produced.emit("Cursor自动重启失败，请手动启动Cursor", "warning")
                                            else:
                                                self.log_produced.emit("根据设置未自动启动Cursor，请手动启动", "info")
                                    else:
                                        self.log_produced.emit("认证信息更新失败", "error")
                                if save_result:
                                    # 检查文件是否存在
                                    if os.path.exists(app_accounts_file):
                                        # 如果是新创建的文件或文件有更新
                                        if not file_existed_before or os.path.getmtime(app_accounts_file) > file_time_before:
                                            try:
                                                # 读取文件验证账号信息是否存在
                                                with open(app_accounts_file, 'r', encoding='utf-8') as f:
                                                    saved_accounts = json.load(f)
                                                    # 检查账号是否在保存的数据中
                                                    for acc in saved_accounts:
                                                        if acc.get('email') == account:
                                                            verification_passed = True
                                                            break
                                            except Exception as e:
                                                self.log_produced.emit(f"验证账号文件失败: {str(e)}", "error")
                                        else:
                                            # 文件存在但未被修改
                                            # 检查是否原来就有该账号
                                            for acc in account_data_before:
                                                if acc.get('email') == account:
                                                    verification_passed = True
                                                    break
                                            
                                            if not verification_passed:
                                                self.log_produced.emit("文件未被更新，保存可能失败", "warning")
                                    else:
                                        self.log_produced.emit(f"保存账号失败: 文件 {app_accounts_file} 不存在", "error")
                                
                                # 第八点五阶段：取消订阅处理
                                cancel_subscription_result = self._handle_subscription_cancellation(token_data['accessToken'])

                                # 最终处理重定向的输出
                                self._process_redirected_output()
                                builtins.print = original_print  # 恢复print函数
                                self.registration_state = "success"
                                self.cancel_subscription_result = cancel_subscription_result  # 保存取消订阅结果
                                return True
                            else:
                                self.log_produced.emit("授权流程失败，无法获取token", "error")
                                # 最终处理重定向的输出
                                self._process_redirected_output()
                                builtins.print = original_print  # 恢复print函数
                                self.registration_state = "failed"
                                self.failure_reason = "授权流程失败，无法获取token"
                                return False
                        else:
                            self.log_produced.emit("账号注册失败", "error")
                            # 最终处理重定向的输出
                            self._process_redirected_output()
                            builtins.print = original_print  # 恢复print函数
                            self.registration_state = "failed"
                            self.failure_reason = "账号注册失败"
                            return False
                    
            except Exception as e:
                import traceback
                # 特别处理关闭时创建线程的错误
                if isinstance(e, RuntimeError) and "can't create new thread" in str(e):
                    self.log_produced.emit("程序正在关闭，无法创建所需线程", "error")
                else:
                    error_info = f"注册过程发生错误: {str(e)}\n{traceback.format_exc()}"
                    self.log_produced.emit(error_info, "error")
                # 确保builtins变量存在并安全地恢复print函数
                try:
                    import builtins
                    builtins.print = original_print  # 恢复print函数
                except Exception:
                    pass  # 如果出现任何问题，静默失败
                return False
            finally:
                # 确保恢复原始print函数
                try:
                    import builtins
                    builtins.print = original_print
                except Exception:
                    pass  # 如果出现任何问题，静默失败
                
                # 关闭浏览器（如果存在）
                try:
                    if hasattr(self, 'browser_manager') and self.browser_manager:
                        self.browser_manager.quit()
                except:
                    pass
                    
                # 不再需要恢复工作目录
                # os.chdir(original_dir)
                
                # 不再需要从sys.path移除cursor-auto目录
                # if self.auto_dir in sys.path:
                #     sys.path.remove(self.auto_dir)
                
        except Exception as e:
            self.log_produced.emit(f"执行注册流程失败: {str(e)}", "error")
            return False
    
    def cleanup(self):
        """清理临时文件"""
        try:
            # self.log_produced.emit("开始执行 AutoRegisterWorker 清理...", "info")
            
            # 清理cursor_pro_keep_alive模块中的全局email_handler变量
            try:
                # 尝试关闭Playwright Browser实例
                if hasattr(self, 'browser') and self.browser:
                    # self.log_produced.emit("尝试关闭 Playwright Browser 实例...", "info")
                    try:
                        if hasattr(self.browser, 'close'):
                            self.browser.close()
                            # self.log_produced.emit("已关闭 Playwright Browser 实例", "info")
                        else:
                            # self.log_produced.emit("self.browser 对象没有找到 close 方法", "info")
                            pass
                    except Exception as e:
                        self.log_produced.emit(f"关闭Browser实例出错: {str(e)}", "warning")
                
                # 尝试再次关闭浏览器管理器
                try:
                    # self.log_produced.emit("尝试再次关闭浏览器管理器/上下文...", "info")
                    if hasattr(self, 'browser_manager') and self.browser_manager:
                        if hasattr(self.browser_manager, 'quit'):
                            self.browser_manager.quit()
                            # self.log_produced.emit("浏览器管理器/上下文 quit() 调用成功", "info")
                        elif hasattr(self.browser_manager, 'close'):
                            self.browser_manager.close()
                            # self.log_produced.emit("浏览器管理器/上下文 close() 调用成功", "info")
                except Exception as e:
                    self.log_produced.emit(f"再次关闭浏览器管理器/上下文时错误: {str(e)}", "warning")
                
                # 使用新的模块重置功能，一次性重置所有全局状态
                try:
                    import core.cursor_auto.cursor_pro_keep_alive as keep_alive
                    
                    # 检查并调用新的重置函数
                    if hasattr(keep_alive, 'reset_module_state'):
                        # 调用模块级重置函数
                        keep_alive.reset_module_state()
                        # self.log_produced.emit("已重置cursor_pro_keep_alive模块状态", "info")
                    else:
                        # 回退到原有的清理逻辑
                        # 显式停止并清理email_handler
                        if hasattr(keep_alive, 'email_handler') and keep_alive.email_handler:
                            # 先调用email_handler的cleanup方法进行资源清理
                            if hasattr(keep_alive.email_handler, 'cleanup'):
                                try:
                                    keep_alive.email_handler.cleanup()
                                    self.log_produced.emit("已调用email_handler.cleanup()方法进行资源清理", "info")
                                except Exception as e:
                                    self.log_produced.emit(f"调用email_handler.cleanup()方法出错: {str(e)}", "warning")
                            
                            # 设置停止标志
                            if hasattr(keep_alive.email_handler, 'set_should_stop'):
                                keep_alive.email_handler.set_should_stop(True)
                                
                            # 清除验证码处理对象
                            keep_alive.email_handler = None
                            # self.log_produced.emit("已清理全局email_handler对象", "info")
                except Exception as e:
                    self.log_produced.emit(f"清理全局email_handler时出错: {str(e)}", "warning")
                    
                # 处理本地email_handler引用
                if hasattr(self, 'email_handler') and self.email_handler:
                    try:
                        if hasattr(self.email_handler, 'cleanup'):
                            self.email_handler.cleanup()
                        if hasattr(self.email_handler, 'set_should_stop'):
                            self.email_handler.set_should_stop(True)
                        self.email_handler = None
                        # self.log_produced.emit("已清理工作线程中的email_handler引用", "info")
                    except Exception as e:
                        self.log_produced.emit(f"清理本地email_handler引用时出错: {str(e)}", "warning")
                    
            except Exception as e:
                self.log_produced.emit(f"尝试清理全局变量时出错: {str(e)}", "warning")
            
            # 1. 恢复原始环境变量
            if hasattr(self, 'original_env') and self.original_env:
                os.environ.clear()
                os.environ.update(self.original_env)
            
            # 2. 清理临时.env文件
            # 获取应用数据目录下的临时目录路径
            app_data_dir = self.get_app_data_dir()
            temp_quota_data_dir = os.path.join(app_data_dir, "temp_quota_data")
            
            # 检查目录是否存在
            if os.path.exists(temp_quota_data_dir):
                # 首先检查传统路径下的.env文件
                if hasattr(self, 'temp_env_file') and self.temp_env_file and os.path.exists(self.temp_env_file):
                    try:
                        os.remove(self.temp_env_file)
                        self.log_produced.emit("已清理临时环境文件", "info")
                    except Exception as e:
                        self.log_produced.emit(f"删除临时文件失败: {self.temp_env_file}, 错误: {str(e)}", "error")
                
                # 然后检查temp_quota_data目录下的.env文件
                env_path_in_quota_dir = os.path.join(temp_quota_data_dir, ".env")
                if os.path.exists(env_path_in_quota_dir):
                    try:
                        os.remove(env_path_in_quota_dir)
                    except Exception as e:
                        self.log_produced.emit(f"删除临时目录中的.env文件失败: {env_path_in_quota_dir}, 错误: {str(e)}", "error")
                
                # 清理temp_quota_data目录下其他临时文件
                try:
                    files_removed = 0
                    for filename in os.listdir(temp_quota_data_dir):
                        if filename.startswith('tmp') or filename == '.env':
                            file_path = os.path.join(temp_quota_data_dir, filename)
                            try:
                                if os.path.isfile(file_path):
                                    os.remove(file_path)
                                    files_removed += 1
                            except Exception as e:
                                self.log_produced.emit(f"删除临时文件失败: {file_path}, 错误: {str(e)}", "error")
                
                    # 只在有多个文件被删除时显示消息
                    if files_removed > 1:
                        self.log_produced.emit(f"清理了{files_removed}个临时文件", "info")
                    
                except Exception as e:
                    self.log_produced.emit(f"清理临时目录中的文件失败: {str(e)}", "error")
            else:
                # 目录不存在时不输出警告
                pass
            
            # 记录browser和browser_manager的关闭状态
            browser_closed = False
            if hasattr(self, 'browser') and self.browser is None:
                browser_closed = True
            
            manager_closed = False
            if hasattr(self, 'browser_manager') and self.browser_manager is None:
                manager_closed = True
                
            # 强制触发垃圾回收
            try:
                import gc
                gc.collect()
                self.log_produced.emit("已触发垃圾回收", "info")
            except:
                pass
                
            # self.log_produced.emit(f"AutoRegisterWorker 清理完成。浏览器实例关闭尝试: {'是' if browser_closed else '否'}, 管理器/上下文关闭尝试: {'是' if manager_closed else '否'}", "info")
        except Exception as e:
            self.log_produced.emit(f"清理过程中发生错误: {str(e)}", "error")
            # 即使发生错误，也尝试删除临时.env文件
            try:
                if hasattr(self, 'temp_env_file') and self.temp_env_file and os.path.exists(self.temp_env_file):
                    os.remove(self.temp_env_file)
            except Exception as clean_error:
                self.log_produced.emit(f"最终删除临时文件失败: {str(clean_error)}", "error")

    def get_app_data_dir(self):
        """获取应用数据目录"""
        try:
            # 导入工具模块
            from utils import get_app_data_dir as get_dir
            return get_dir()
        except ImportError:
            # 备用方法：自己实现获取应用数据目录的逻辑
            app_data = None
            if sys.platform == "win32":
                app_data = os.environ.get("APPDATA")
                if app_data:
                    return os.path.join(app_data, "YCursor")
            elif sys.platform == "darwin":
                app_data = os.path.expanduser("~/Library/Application Support")
                if app_data:
                    return os.path.join(app_data, "YCursor")
            else:  # Linux和其他平台
                app_data = os.path.expanduser("~/.config")
                if app_data:
                    return os.path.join(app_data, "YCursor")
            
            # 如果无法确定应用数据目录，使用临时目录
            if not app_data:
                return os.path.join(tempfile.gettempdir(), "YCursor")

    def _emit_log_with_deduplication(self, msg, log_type):
        """发送日志，并进行去重处理"""
        # 检查是否是验证码相关消息
        stripped_msg = self._strip_log_prefix(msg)
        is_verification_related = any(keyword in msg for keyword in 
                                   ["正在获取邮箱验证码", "验证码匹配规则", "从临时邮箱获取邮件列表", 
                                    "获取邮件内容", "成功从临时邮箱获取验证码", "正在输入验证码", 
                                    "验证码输入完成"])
        
        # 如果不是验证码相关消息，或者是验证码相关消息但未处理过，则发送
        if not is_verification_related or stripped_msg not in self._recent_log_messages:
            self.log_produced.emit(stripped_msg, log_type)
            # 如果是验证码相关消息，记录已发送
            if is_verification_related:
                self._recent_log_messages.add(stripped_msg)

    def _is_interpreter_shutting_down(self):
        """检查Python解释器是否正在关闭"""
        try:
            # 尝试创建一个临时线程并立即关闭它
            # 如果解释器正在关闭，这将引发RuntimeError
            import threading
            test_thread = threading.Thread(target=lambda: None)
            test_thread.daemon = True
            test_thread.start()
            test_thread.join(0.1)  # 等待很短的时间
            return False  # 成功创建线程，解释器没有关闭
        except RuntimeError as e:
            if "can't create new thread" in str(e) and "interpreter shutdown" in str(e):
                return True  # 解释器正在关闭
            return False  # 其他运行时错误
        except:
            return False  # 其他异常

    def kill_cursor_process(self):
        """
        杀死Cursor进程
        返回：是否成功杀死Cursor进程的布尔值
        """
        try:
            cursor_killed = False
            
            # Windows系统
            if sys.platform == "win32":
                # 使用tasklist查找进程，仅匹配确切的进程名Cursor.exe
                find_cmd = 'tasklist /FI "IMAGENAME eq Cursor.exe" /FO CSV /NH'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if "Cursor.exe" in result.stdout:
                    # 使用taskkill杀死进程
                    kill_cmd = 'taskkill /F /IM Cursor.exe'
                    subprocess.run(kill_cmd, shell=True, capture_output=True)
                    cursor_killed = True
                    self.log_produced.emit("Windows: 已结束Cursor进程", "info")
            
            # macOS系统
            elif sys.platform == "darwin":
                # 查找进程ID，确保只找 /Applications/Cursor.app 的进程
                find_cmd = 'pgrep -f "^/Applications/Cursor.app/Contents/MacOS/Cursor"'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if result.stdout.strip():
                    # 杀死进程
                    kill_cmd = f'kill -9 {result.stdout.strip()}'
                    subprocess.run(kill_cmd, shell=True)
                    cursor_killed = True
                    self.log_produced.emit("macOS: 已结束Cursor进程", "info")
            
            # Linux系统
            elif sys.platform == "linux":
                # 查找进程ID，使用更精确的匹配
                find_cmd = 'pgrep -f "^/usr/bin/cursor|^/usr/local/bin/cursor|^/opt/cursor/cursor"'
                result = subprocess.run(find_cmd, shell=True, capture_output=True, text=True)
                
                if result.stdout.strip():
                    # 杀死进程
                    kill_cmd = f'kill -9 {result.stdout.strip()}'
                    subprocess.run(kill_cmd, shell=True)
                    cursor_killed = True
                    self.log_produced.emit("Linux: 已结束Cursor进程", "info")
            
            return cursor_killed
        except Exception as e:
            self.log_produced.emit(f"结束Cursor进程时出错: {str(e)}", "error")
            return False
    
    def start_cursor_app(self):
        """
        启动Cursor应用程序，确保其作为独立进程运行且不显示终端窗口
        """
        try:
            # Windows系统
            if sys.platform == "win32":
                cursor_path = os.path.join(os.getenv("LOCALAPPDATA"), "Programs", "cursor", "Cursor.exe")
                if os.path.exists(cursor_path):
                    # 使用Windows内置的start命令启动程序，不显示命令提示符
                    # /B 参数表示不创建新的控制台窗口
                    # start命令会创建一个完全独立的进程
                    subprocess.run('start /B "" "' + cursor_path + '"', shell=True)
                    self.log_produced.emit("Windows: 已启动Cursor应用", "info")
                    return True
            
            # macOS系统
            elif sys.platform == "darwin":
                # 使用open命令启动，-n参数强制创建新实例
                # -g参数使应用在后台启动而不获取焦点
                subprocess.run(["open", "-n", "-g", "-a", "Cursor"], shell=False)
                self.log_produced.emit("macOS: 已启动Cursor应用", "info")
                return True
            
            # Linux系统
            elif sys.platform == "linux":
                # 尝试多个可能的路径
                for cursor_path in ["/usr/bin/cursor", "/usr/local/bin/cursor", "/opt/cursor/cursor"]:
                    if os.path.exists(cursor_path):
                        # 使用nohup启动，并在后台运行，禁止输出
                        subprocess.run(
                            f"nohup {cursor_path} >/dev/null 2>&1 & disown",
                            shell=True,
                            start_new_session=True
                        )
                        self.log_produced.emit("Linux: 已启动Cursor应用", "info")
                        return True
            
            # 如果没有找到可执行文件
            self.log_produced.emit("无法找到Cursor应用程序", "error")
            return False
        except Exception as e:
            self.log_produced.emit(f"启动Cursor应用程序时出错: {str(e)}", "error")
            return False

    def _save_error_to_log(self, filename, message):
        """将错误信息保存到日志文件中，以便在清理后仍能查看错误详情，但不显示保存信息"""
        try:
            # 获取应用数据目录
            app_data_dir = self.get_app_data_dir()
            # 创建日志目录
            log_dir = os.path.join(app_data_dir, "logs")
            os.makedirs(log_dir, exist_ok=True)
            
            # 完整的日志文件路径
            log_file = os.path.join(log_dir, filename)
            
            # 获取当前时间
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            
            # 写入错误信息
            with open(log_file, "a", encoding="utf-8") as f:
                f.write(f"\n--- 错误时间: {current_time} ---\n")
                f.write(message)
                f.write("\n\n")
                
            # 不再显示保存位置信息
            # self.log_produced.emit(f"错误详情已保存到: {log_file}", "info")
        except Exception as e:
            # 保存日志失败时不应影响主流程，也不显示警告
            pass

    def _handle_subscription_cancellation(self, access_token):
        """处理取消订阅逻辑

        Args:
            access_token: 从第六阶段获取的访问令牌

        Returns:
            dict: 包含取消订阅结果的字典
        """
        try:
            # 检查绕过绑卡配置
            bypass_card_enabled = True  # 默认开启绕过绑卡
            try:
                settings_file = os.path.join(get_app_data_dir(), "settings.json")
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        bypass_card_enabled = settings.get("auto_register_bypass_card", True)
            except Exception as e:
                self.log_produced.emit(f"读取绕过绑卡配置失败，使用默认值: {str(e)}", "debug")

            # 如果开启了绕过绑卡，跳过取消订阅流程
            if bypass_card_enabled:
                self.log_produced.emit("已开启绕过绑卡，跳过取消订阅流程", "info")
                return {
                    "success": True,
                    "message": "已跳过取消订阅（绕过绑卡模式）",
                    "skipped": True
                }

            # 未开启绕过绑卡，执行取消订阅流程
            self.log_produced.emit("未开启绕过绑卡，开始取消订阅流程...", "info")

            # 构建完整的token（按照账户页面的格式）
            fixed_prefix = "user_01000000000000000000000000%3A%3A"
            full_token = fixed_prefix + access_token

            # 创建取消订阅管理器
            subscription_manager = self._create_subscription_manager(full_token)

            # 执行取消订阅流程
            cancel_result = subscription_manager.cancel_subscription_flow(refund=False)

            if cancel_result:
                self.log_produced.emit("订阅取消成功", "success")
                return {
                    "success": True,
                    "message": "订阅已成功取消",
                    "skipped": False
                }
            else:
                # 获取详细的错误信息
                error_details = subscription_manager.get_last_error()
                self.log_produced.emit(f"订阅取消失败: {error_details}", "error")
                return {
                    "success": False,
                    "message": f"订阅取消失败: {error_details}",
                    "skipped": False
                }

        except Exception as e:
            error_msg = f"取消订阅过程中出现异常: {str(e)}"
            self.log_produced.emit(error_msg, "error")
            return {
                "success": False,
                "message": f"取消订阅异常: {str(e)}",
                "skipped": False
            }

    def _create_subscription_manager(self, full_token):
        """创建订阅管理器

        Args:
            full_token: 完整的token字符串

        Returns:
            CursorSubscriptionManager: 订阅管理器实例
        """
        import requests
        import re
        from urllib.parse import urlparse, parse_qs

        class CursorSubscriptionManager:
            def __init__(self, token):
                self.cursor_token = token
                self.session = requests.Session()
                self.stripe_session_url = None
                self.live_session_id = None
                self.bps_session_id = None
                self.subscription_id = None
                self.csrf_token = None
                self.bearer_token = None
                self.last_error = "未知错误"
                self.log_callback = None

            def set_log_callback(self, callback):
                """设置日志回调函数"""
                self.log_callback = callback

            def log(self, message, level="info"):
                """记录日志"""
                if self.log_callback:
                    self.log_callback(f"[取消订阅] {message}", level)

            def get_last_error(self):
                """获取最后的错误信息"""
                return self.last_error

            def get_stripe_session_url(self):
                """获取Stripe会话URL"""
                cookies = {
                    'NEXT_LOCALE': 'en',
                    'WorkosCursorSessionToken': self.cursor_token
                }

                headers = {
                    'accept': '*/*',
                    'accept-language': 'zh-CN,zh;q=0.9',
                    'if-none-match': '"zc6lt7pm693k"',
                    'priority': 'u=1, i',
                    'referer': 'https://cursor.com/dashboard',
                    'sec-ch-ua': '"Not)A;Brand";v="8", "Chromium";v="138", "Microsoft Edge";v="138"',
                    'sec-ch-ua-arch': 'x86',
                    'sec-ch-ua-bitness': '64',
                    'sec-ch-ua-mobile': '?0',
                    'sec-ch-ua-platform': 'Windows',
                    'sec-ch-ua-platform-version': '19.0.0',
                    'sec-fetch-dest': 'empty',
                    'sec-fetch-mode': 'cors',
                    'sec-fetch-site': 'same-origin',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********'
                }

                try:
                    response = self.session.get('https://cursor.com/api/stripeSession',
                                              cookies=cookies, headers=headers)
                    if response.status_code == 200:
                        stripe_url = response.text.strip().replace('"', '')
                        self.stripe_session_url = stripe_url
                        # 从URL中提取live session_id
                        url_parts = stripe_url.split('/')
                        if 'session' in url_parts:
                            session_index = url_parts.index('session')
                            if session_index + 1 < len(url_parts):
                                self.live_session_id = url_parts[session_index + 1]
                        return True
                    else:
                        self.last_error = f"获取Stripe会话URL失败，状态码: {response.status_code}"
                        self.log(self.last_error, "error")
                        return False
                except Exception as e:
                    self.last_error = f"获取Stripe会话URL异常: {str(e)}"
                    self.log(self.last_error, "error")
                    return False

            def get_session_info(self):
                """获取会话信息，包括Bearer token和真实的bps session ID"""
                if not self.live_session_id:
                    self.last_error = "缺少live session ID"
                    self.log(self.last_error, "error")
                    return False

                # 访问Stripe会话页面获取页面内容
                try:
                    response = self.session.get(self.stripe_session_url)
                    if response.status_code == 200:
                        html_content = response.text

                        # 从cookies中提取CSRF token
                        for cookie in self.session.cookies:
                            if 'csrf' in cookie.name.lower():
                                self.csrf_token = cookie.value
                                break

                        # 从页面HTML中提取session_api_key (Bearer token)
                        bearer_patterns = [
                            r'"session_api_key":"(ek_live_[^"]+)"',
                            r'&quot;session_api_key&quot;:&quot;(ek_live_[^&]+)&quot;',
                            r'session_api_key["\']?\s*:\s*["\']?(ek_live_[^"\']+)["\']?'
                        ]

                        for pattern in bearer_patterns:
                            match = re.search(pattern, html_content)
                            if match:
                                self.bearer_token = match.group(1)
                                break

                        # 从页面HTML中提取portal_session_id (真实的bps_ session ID)
                        bps_patterns = [
                            r'"portal_session_id":"(bps_[^"]+)"',
                            r'&quot;portal_session_id&quot;:&quot;(bps_[^&]+)&quot;',
                            r'portal_session["\']?\s*:\s*["\']?(bps_[^"\']+)["\']?',
                            r'"id":"(bps_[^"]+)".*"object":"billing_portal\.session"'
                        ]

                        for pattern in bps_patterns:
                            match = re.search(pattern, html_content, re.DOTALL)
                            if match:
                                self.bps_session_id = match.group(1)
                                break

                        if not self.bearer_token:
                            self.last_error = "无法从页面提取Bearer token"
                            self.log(self.last_error, "error")
                            return False

                        if not self.bps_session_id:
                            return self.get_bps_session_from_api()

                        return True
                    else:
                        self.last_error = f"访问Stripe会话页面失败，状态码: {response.status_code}"
                        self.log(self.last_error, "error")
                        return False
                except Exception as e:
                    self.last_error = f"获取会话信息异常: {str(e)}"
                    self.log(self.last_error, "error")
                    return False

            def get_bps_session_from_api(self):
                """通过API获取真实的bps session ID"""
                if not self.bearer_token:
                    self.last_error = "缺少Bearer token，无法通过API获取BPS session ID"
                    self.log(self.last_error, "error")
                    return False

                # 尝试多个可能的API端点来获取session信息
                possible_urls = [
                    f"https://billing.stripe.com/v1/billing_portal/sessions/{self.live_session_id}",
                    "https://billing.stripe.com/v1/billing_portal/sessions"
                ]

                headers = {
                    'accept': 'application/json',
                    'authorization': f'Bearer {self.bearer_token}',
                    'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
                    'stripe-version': '2025-04-30.basil',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }

                for url in possible_urls:
                    try:
                        response = self.session.get(url, headers=headers)
                        if response.status_code == 200:
                            data = response.json()
                            if 'id' in data and data['id'].startswith('bps_'):
                                self.bps_session_id = data['id']
                                return True
                    except Exception as e:
                        continue

                self.last_error = "所有API端点都无法获取BPS session ID"
                self.log(self.last_error, "error")
                return False

            def get_subscription_id(self):
                """获取订阅ID"""
                if not self.bps_session_id or not self.bearer_token:
                    self.last_error = "缺少BPS session ID或Bearer token"
                    self.log(self.last_error, "error")
                    return False

                # 构建获取订阅列表的URL
                url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions"

                headers = {
                    'accept': 'application/json',
                    'authorization': f'Bearer {self.bearer_token}',
                    'accept-language': 'zh-Hans, zh-CN',
                    'browser-language': 'zh-CN',
                    'referer': self.stripe_session_url,
                    'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
                    'stripe-livemode': 'true',
                    'stripe-version': '2025-04-30.basil',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'x-requested-with': 'XMLHttpRequest'
                }

                # 如果有CSRF token，也添加进去
                if self.csrf_token:
                    headers['x-stripe-csrf-token'] = self.csrf_token

                params = {
                    'include_only[]': [
                        'has_more',
                        'data.id',
                        'data.status',
                        'data.is_cancelable'
                    ]
                }

                try:
                    response = self.session.get(url, headers=headers, params=params)
                    if response.status_code == 200:
                        data = response.json()
                        if 'data' in data and len(data['data']) > 0:
                            for subscription in data['data']:
                                if subscription.get('is_cancelable', False):
                                    self.subscription_id = subscription['id']
                                    return True
                            self.last_error = "没有找到可取消的订阅"
                            self.log(self.last_error, "error")
                            return False
                        else:
                            self.last_error = "订阅列表为空"
                            self.log(self.last_error, "error")
                            return False
                    else:
                        self.last_error = f"获取订阅列表失败，状态码: {response.status_code}"
                        self.log(self.last_error, "error")
                        return False
                except Exception as e:
                    self.last_error = f"获取订阅ID异常: {str(e)}"
                    self.log(self.last_error, "error")
                    return False

            def cancel_subscription(self, refund=False):
                """取消订阅"""
                if not all([self.bps_session_id, self.subscription_id, self.bearer_token]):
                    self.last_error = "缺少必要的参数（BPS session ID、订阅ID或Bearer token）"
                    self.log(self.last_error, "error")
                    return False

                # 构建取消订阅的URL
                url = f"https://billing.stripe.com/v1/billing_portal/sessions/{self.bps_session_id}/subscriptions/{self.subscription_id}/cancel"

                headers = {
                    'accept': 'application/json',
                    'authorization': f'Bearer {self.bearer_token}',
                    'accept-language': 'zh-Hans, zh-CN',
                    'browser-language': 'zh-CN',
                    'content-type': 'application/x-www-form-urlencoded',
                    'origin': 'https://billing.stripe.com',
                    'referer': f"https://billing.stripe.com/p/session/{self.live_session_id}/subscriptions/{self.subscription_id}/cancel",
                    'stripe-account': 'acct_1Lb5LzB4TZWxSIGU',
                    'stripe-livemode': 'true',
                    'stripe-version': '2025-04-30.basil',
                    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********',
                    'x-requested-with': 'XMLHttpRequest'
                }

                # 如果有CSRF token，也添加进去
                if self.csrf_token:
                    headers['x-stripe-csrf-token'] = self.csrf_token

                params = {
                    'include_only[]': 'id'
                }

                data = {
                    'refund': 'true' if refund else 'false'
                }

                try:
                    response = self.session.post(url, headers=headers, params=params, data=data)

                    if response.status_code == 200:
                        result = response.json()
                        if 'id' in result:
                            return True
                        else:
                            self.last_error = "取消订阅响应中缺少ID字段"
                            self.log(self.last_error, "error")
                            return False
                    else:
                        self.last_error = f"取消订阅失败，状态码: {response.status_code}, 响应: {response.text}"
                        self.log(self.last_error, "error")
                        return False

                except Exception as e:
                    self.last_error = f"取消订阅异常: {str(e)}"
                    self.log(self.last_error, "error")
                    return False

            def cancel_subscription_flow(self, refund=False):
                """完整的取消订阅流程"""
                # 步骤1: 获取Stripe会话URL
                if not self.get_stripe_session_url():
                    return False

                # 步骤2: 获取会话信息（Bearer token和BPS session ID）
                if not self.get_session_info():
                    return False

                # 步骤3: 获取订阅ID
                if not self.get_subscription_id():
                    return False

                # 步骤4: 取消订阅
                return self.cancel_subscription(refund)

        manager = CursorSubscriptionManager(full_token)
        manager.set_log_callback(self.log_produced.emit)
        return manager

class AutoRegisterDialog(StyledDialog):
    """自动注册对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent, "自动注册Cursor账号", 500)
        self.worker = None
        self.log_text_edit = None
        self.progress_bar = None
        self.toast = None
        self.registration_completed = False  # 添加一个标志来跟踪注册是否已完成
        
        # 初始化UI
        self.init_ui()
        
        # 添加浮动提示
        self.add_floating_tip("按 Esc键 关闭对话框")
        
        # 加载设置
        self.settings_file = os.path.join(get_app_data_dir(), "settings.json")
        self.settings = self.load_settings()
        
        # 加载功能配置
        self.function_settings = self.load_function_settings()
        
        # 自动开始注册流程
        QTimer.singleShot(100, self.start_register)
    
    def init_ui(self):
        """初始化UI"""
        # 创建日志文本框
        self.log_text_edit = QPlainTextEdit()
        self.log_text_edit.setReadOnly(True)
        self.log_text_edit.setLineWrapMode(QPlainTextEdit.LineWrapMode.WidgetWidth)
        self.log_text_edit.setMinimumHeight(200)
        self.log_text_edit.setStyleSheet(f"""
            QPlainTextEdit {{
                background-color: #0e1015;
                color: {Theme.TEXT_PRIMARY};
                border-radius: 10px;
                padding: 10px;
                font-family: 'Consolas', 'Courier New', monospace;
                font-size: 13px;
                border: none;
                selection-background-color: {Theme.ACCENT};
                selection-color: white;
            }}
            QScrollBar:vertical {{
                background-color: transparent;
                width: 8px;
                margin: 2px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #3a3f4c;
                min-height: 20px;
                border-radius: 4px;
            }}
            QScrollBar::handle:vertical:hover {{
                background-color: {Theme.ACCENT};
            }}
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical,
            QScrollBar::add-page:vertical, QScrollBar::sub-page:vertical {{
                background: none;
                border: none;
                height: 0px;
            }}
        """)
        
        # 启用右键菜单
        self.log_text_edit.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.log_text_edit.customContextMenuRequested.connect(self._show_log_context_menu)
        
        # 添加到对话框
        self.addWidget(self.log_text_edit)
        
        # 添加进度条
        self.progress_bar = StyledProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setValue(0)
        self.progress_bar.setFixedHeight(20)
        self.progress_bar.setFormat("")
        self.progress_bar.setStyleSheet(f"""
            QProgressBar {{
                background-color: #1a1e24;
                border-radius: 12px;
                text-align: center;
                color: transparent;
            }}
            
            QProgressBar::chunk {{
                background-color: {Theme.ACCENT};
                border-radius: 12px;
            }}
        """)
        
        # 添加到对话框
        self.addWidget(self.progress_bar)

        # 添加手动验证按钮（初始隐藏）
        self.manual_cf_button = QPushButton("我已完成人机验证，继续下一步")
        self.manual_cf_button.setFixedHeight(40)
        self.manual_cf_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {Theme.ACCENT};
                transform: translateY(1px);
            }}
        """)
        self.manual_cf_button.clicked.connect(self.on_manual_cf_completed)
        self.manual_cf_button.hide()  # 初始隐藏

        # 添加到对话框
        self.addWidget(self.manual_cf_button)

        # 添加手动绑卡确认按钮（初始隐藏）
        self.manual_card_button = QPushButton("我已完成绑定，继续")
        self.manual_card_button.setFixedHeight(40)
        self.manual_card_button.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: bold;
                padding: 10px 20px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {Theme.ACCENT};
                transform: translateY(1px);
            }}
        """)
        self.manual_card_button.clicked.connect(self.on_manual_card_completed)
        self.manual_card_button.hide()  # 初始隐藏

        # 添加到对话框
        self.addWidget(self.manual_card_button)
    
    def _show_log_context_menu(self, position):
        """显示日志文本区域的自定义上下文菜单"""
        try:
            # 创建自定义上下文菜单
            context_menu = QMenu(self)
            
            # 设置菜单样式与应用的暗色主题匹配
            context_menu.setStyleSheet(f"""
                QMenu {{
                    background-color: #1A1D24;
                    color: {Theme.TEXT_PRIMARY};
                    border: 1px solid {Theme.BORDER};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 5px;
                    font-weight: bold;
                }}
                QMenu::item {{
                    padding: 5px 15px;
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                }}
                QMenu::item:selected {{
                    background-color: {Theme.ACCENT};
                    color: white;
                }}
                QMenu::separator {{
                    height: 1px;
                    background-color: {Theme.BORDER};
                    margin: 5px;
                }}
            """)
            
            # 添加菜单项
            copy_action = context_menu.addAction("复制")
            select_all_action = context_menu.addAction("全选")
            
            context_menu.addSeparator()
            
            copy_all_action = context_menu.addAction("复制全部日志")
            
            # 获取选中的文本
            has_selection = self.log_text_edit.textCursor().hasSelection()
            
            # 根据是否有选中文本启用/禁用复制操作
            copy_action.setEnabled(has_selection)
            
            # 显示菜单并处理选择
            action = context_menu.exec(self.log_text_edit.mapToGlobal(position))
            
            # 处理动作
            if action == copy_action and has_selection:
                # 复制选中文本
                clipboard = QApplication.clipboard()
                clipboard.setText(self.log_text_edit.textCursor().selectedText())
            elif action == select_all_action:
                # 全选
                self.log_text_edit.selectAll()
            elif action == copy_all_action:
                # 复制全部文本
                clipboard = QApplication.clipboard()
                clipboard.setText(self.log_text_edit.toPlainText())
                self.show_toast("已复制全部日志内容")
                
        except Exception as e:
            print(f"显示上下文菜单时出错: {str(e)}")
    
    def add_log(self, text, log_type="info"):
        """添加日志"""
        # 获取时间戳
        timestamp = QTime.currentTime().toString("hh:mm:ss")
        timestamp_text = f"[{timestamp}] "
        
        # 标准化日志类型（支持中英文类型名称）
        log_type_lower = log_type.lower() if isinstance(log_type, str) else "info"
        
        # 日志类型映射字典
        type_mapping = {
            # 英文类型
            "info": "info",
            "warning": "warning",
            "error": "error",
            "debug": "debug",
            "process": "process",
            "success": "success",
            "hint": "hint", # 添加 hint 映射
            # 中文类型
            "信息": "info",
            "警告": "warning",
            "错误": "error",
            "调试": "debug",
            "进行": "process",
            "成功": "success",
            "提示": "hint" # 添加 hint 中文映射 (如果需要)
        }
        
        # 获取标准化的日志类型
        standard_type = type_mapping.get(log_type_lower, "info")
        
        # 检查是否有特殊的验证码信息
        is_verification_code = False
        if "验证码" in text:
            is_verification_code = True
            # 只有在明确成功获取验证码时才标记为success，不包含"失败"字样
            if "成功" in text and "失败" not in text:
                standard_type = "success"
            elif "失败" in text:
                standard_type = "error"
            # 获取验证码的状态未知时保持原始类型不变
        
        # 检查是否是邮件内容相关
        is_email_content = any(keyword in text for keyword in ["验证码邮件内容", "邮件内容:", "邮件正文:", "发件人:", "主题:"])
        if is_email_content:
            # 邮件内容使用特殊颜色
            standard_type = "info"
        
        # 根据标准化日志类型设置前缀和颜色
        if standard_type == "error":
            color = QColor(Theme.ERROR)
            prefix = "❌ "
            # 为错误消息添加更明显的样式
            formatted_text = f"{timestamp_text}{prefix}{text}"
            
            # 使用HTML格式使整行显示为红色并加粗
            html_text = f"<span style='color: {Theme.ERROR}; font-weight: bold;'>{formatted_text}</span>"
            
            # 在纯文本模式下添加文本
            self.log_text_edit.appendHtml(html_text)
        elif standard_type == "hint": # 添加 hint 类型处理
            color = QColor("#9370DB") # 紫色
            prefix = "💡 " # 提示图标
            formatted_text = f"{timestamp_text}{prefix}{text}"
            html_text = f"<span style='color: {color.name()};'>{formatted_text}</span>"
            self.log_text_edit.appendHtml(html_text)
        else:
            # 其他类型日志设置相应的前缀和颜色
            if standard_type == "warning":
                color = QColor(Theme.WARNING)
                prefix = "⚠️ "
            elif standard_type == "info":
                # 邮件内容使用不同的颜色
                if is_email_content:
                    color = QColor("#00BCD4")  # 使用更明显的蓝绿色
                    prefix = "📧 "
                else:
                    color = QColor(Theme.TEXT_PRIMARY)
                    prefix = "ℹ️ "
            elif standard_type == "debug":
                color = QColor(Theme.TEXT_SECONDARY)
                prefix = "🔍 "
            elif standard_type == "process":
                color = QColor(Theme.ACCENT)
                prefix = "🔄 "
            elif standard_type == "success":
                color = QColor("#2ecc71")  # 绿色
                prefix = "✅ "
            else:
                color = QColor(Theme.TEXT_PRIMARY)
                prefix = "📌 "  # 默认前缀
            
            # 邮件正文使用特殊样式
            if "邮件正文:" in text:
                # 邮件正文标题行
                html_text = f"<span style='color: {color.name()}; font-weight: bold;'>{timestamp_text}{prefix}{text}</span>"
                self.log_text_edit.appendHtml(html_text)
            elif text.startswith("  ") and is_email_content:
                # 邮件内容的缩进行，使用特殊样式
                html_text = f"<span style='color: {color.name()}; margin-left: 20px;'>{timestamp_text}{prefix}{text}</span>"
                self.log_text_edit.appendHtml(html_text)
            else:
                # 设置文本颜色并添加到日志
                current_cursor = self.log_text_edit.textCursor()
                current_format = current_cursor.charFormat()
                current_format.setForeground(color)
                current_cursor.setCharFormat(current_format)
                
                # 保留原始缩进，但在缩进前添加时间戳和前缀
                if text.startswith("  "):
                    # 验证码内容和邮件内容的特殊处理
                    if is_verification_code or is_email_content:
                        # 对于缩进内容（如邮件内容），使用不同样式
                        indented_text = f"{timestamp_text}{prefix}{text}"
                        html_text = f"<span style='color: {color.name()}; margin-left: 20px;'>{indented_text}</span>"
                        self.log_text_edit.appendHtml(html_text)
                    else:
                        # 普通的缩进内容
                        self.log_text_edit.appendPlainText(f"{timestamp_text}{prefix}{text}")
                else:
                    # 普通日志
                    self.log_text_edit.appendPlainText(f"{timestamp_text}{prefix}{text}")
        
        # 滚动到底部
        self.log_text_edit.verticalScrollBar().setValue(
            self.log_text_edit.verticalScrollBar().maximum()
        )
        
        # 记录到系统日志
        if standard_type == "info":
            info(f"[自动注册] {text}")
        elif standard_type == "warning":
            warning(f"[自动注册] {text}")
        elif standard_type == "error":
            error(f"[自动注册] {text}")
        elif standard_type == "hint": # 在系统日志中也记录 hint
            info(f"[自动注册][提示] {text}")
        else:
            info(f"[自动注册] {text}")  # 默认记录为info
    
    def show_toast(self, message, is_error=False, duration=2000):
        """显示Toast消息"""
        # 获取主窗口作为toast的父窗口
        main_window = self.parent()
        while main_window and not main_window.isWindow():
            main_window = main_window.parent()
            
        if main_window is None:
            main_window = self
            
        # 使用主窗口的toast系统或者创建新的toast
        if hasattr(main_window, 'toast_queue') and main_window.toast_queue:
            main_window.toast_queue.add_message(message, duration, is_error)
        else:
            # 如果主窗口没有toast_queue，直接创建ToastMessage
            if self.toast is not None and self.toast.isVisible():
                self.toast.close()
                
            self.toast = ToastMessage(
                main_window,  # 使用主窗口作为父窗口
                message,
                duration,
                Theme.ERROR if is_error else None
            )
            self.toast.showToast()
    
    def update_progress(self, current, total):
        """更新进度条"""
        # 确保进度值在有效范围内
        progress = min(max(current, 0), total)
        self.progress_bar.setValue(progress)
    
    def on_operation_finished(self, success, message):
        """操作完成处理函数"""
        # 设置注册已完成标志
        self.registration_completed = True
        
        # 计算注册耗时并输出到日志
        if hasattr(self.worker, 'start_time') and self.worker.start_time > 0:
            elapsed_time = time.time() - self.worker.start_time
            self.add_log(f"本次注册用时: {elapsed_time:.2f}秒", "info")
        
        if success:
            # 成功完成
            self.add_log(f"操作成功: {message}", "info")
            self.show_toast("自动注册成功！", False, 3000)

            # 显示取消订阅结果的toast通知
            self._show_subscription_cancellation_toast()
            
            # 检查是否为仅注册模式
            register_only = self.function_settings.get("register_only", False)
            
            # 检查worker的registration_state，确保状态为success
            worker_registration_state = "unknown"
            if hasattr(self.worker, 'registration_state'):
                worker_registration_state = self.worker.registration_state
            
            # 额外检查：确保worker返回的状态确实是成功
            if worker_registration_state != "success":
                self.add_log(f"警告: Worker状态({worker_registration_state})与操作结果不一致，跳过保存账号", "warning")
                return
                
            # 如果是仅注册模式，显示相关日志
            if register_only:
                self.add_log("仅注册模式：跳过更新应用认证信息", "info")
            # 注释：移除了保存账号到账户列表的功能，避免重复保存
                
            # 检查是否应该自动关闭对话框
            should_auto_close = False # 默认不自动关闭
            try:
                # 读取设置文件
                settings_file = os.path.join(get_app_data_dir(), "settings.json")
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        # 检查自动关闭设置
                        if settings.get("auto_close_dialog_on_success", True):
                            should_auto_close = True
            except Exception as e:
                print(f"读取设置时出错: {str(e)}")

            if should_auto_close:
                self.close() # 自动关闭
            else:
                # 如果不自动关闭，添加提示 (使用 hint 类型)
                # self.add_log("按 ESC 键 关闭对话框", "hint")
                pass
        else:
            # 操作失败
            # self.add_log(f"操作失败: {message}", "error")
            self.show_toast(f"自动注册失败: {message}", True, 3000)
            # 添加提示 (使用 hint 类型)
            # self.add_log("按 ESC 键 关闭对话框", "hint")

    def _show_subscription_cancellation_toast(self):
        """显示取消订阅结果的toast通知"""
        try:
            # 检查worker是否有取消订阅结果
            if hasattr(self.worker, 'cancel_subscription_result') and self.worker.cancel_subscription_result:
                result = self.worker.cancel_subscription_result

                if result.get("skipped", False):
                    # 跳过了取消订阅
                    QTimer.singleShot(3500, lambda: self.show_toast(result.get("message", "已跳过取消订阅"), False, 2500))
                elif result.get("success", False):
                    # 取消订阅成功
                    QTimer.singleShot(3500, lambda: self.show_toast(result.get("message", "订阅已成功取消"), False, 2500))
                else:
                    # 取消订阅失败
                    QTimer.singleShot(3500, lambda: self.show_toast(result.get("message", "订阅取消失败"), True, 3000))
        except Exception as e:
            # 如果获取取消订阅结果时出错，不影响主流程
            pass
        
        # 启用关闭按钮（如果存在）
        if hasattr(self, 'close_button') and self.close_button:
            self.close_button.setEnabled(True)
    
    # _save_current_account方法已删除，不再需要重复保存账号
    
    def on_manual_cf_completed(self):
        """用户完成手动CF验证后的回调"""
        self.manual_cf_button.hide()
        if hasattr(self.worker, 'manual_cf_event'):
            self.worker.manual_cf_event.set()  # 通知工作线程继续
        self.add_log("用户已完成人机验证，继续自动注册流程", "info")

    def on_manual_card_completed(self):
        """用户完成手动绑卡后的回调"""
        self.manual_card_button.hide()
        if hasattr(self.worker, 'manual_card_event'):
            self.worker.manual_card_event.set()  # 通知工作线程继续
        self.add_log("用户已完成绑定，继续自动注册流程", "info")

    def show_manual_cf_button(self):
        """显示手动CF验证按钮"""
        self.manual_cf_button.show()
        self.add_log("检测到CF人机验证，请手动完成验证后点击按钮继续", "warning")

    def show_manual_card_button(self):
        """显示手动绑卡确认按钮"""
        self.manual_card_button.show()
        self.add_log("请完成绑定后点击按钮继续", "warning")

    def start_register(self):
        """开始自动注册流程"""
        # 检查设置是否有效
        if not self.validate_settings():
            return

        # 创建工作线程，传递功能配置
        self.worker = AutoRegisterWorker(self.settings)
        
        # 设置仅注册功能状态
        register_only = self.function_settings.get("register_only", False)
        self.worker.register_only = register_only
        
        # 设置注册方案
        register_method = self.function_settings.get("register_method", 1)
        self.worker.register_method = register_method
        
        # 记录当前配置的方案信息
        method_info = "方案一" if register_method == 1 else "方案二"
        self.add_log(f"将使用 {method_info} 进行自动注册", "info")
        
        # 如果是仅注册模式，记录日志
        if register_only:
            self.add_log("启用\"仅注册\"模式：将不会更新应用认证信息", "info")
        
        # 连接信号
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.log_produced.connect(self.add_log)
        self.worker.operation_finished.connect(self.on_operation_finished)
        self.worker.manual_cf_required.connect(self.show_manual_cf_button)
        self.worker.manual_card_required.connect(self.show_manual_card_button)
        
        # 开始执行
        self.worker.start()
    
    def load_settings(self):
        """从settings.json加载设置"""
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"加载设置文件出错: {str(e)}")
                
        return {}
    
    def validate_settings(self):
        """验证设置是否有效"""
        # 检查域名
        domain = self.settings.get("auto_register_domain", "")
        if not domain:
            self.add_log("域名配置不能为空，请先在设置中配置", "error")
            self.show_toast("域名配置不能为空", True)
            return False
            
        # 解析域名字符串，支持多个域名（用逗号分隔）
        domains = [d.strip() for d in domain.split(",") if d.strip()]
        if not domains:
            self.add_log("域名配置无效，请检查格式", "error")
            self.show_toast("域名配置无效", True)
            return False
            
        # 检查每个域名的格式
        import re
        domain_pattern = re.compile(r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$')
        
        invalid_domains = [d for d in domains if not domain_pattern.match(d)]
        if invalid_domains:
            self.add_log(f"以下域名格式无效: {', '.join(invalid_domains)}", "error")
            self.show_toast("存在无效域名格式", True)
            return False
            
        # 域名验证通过，继续检查邮箱设置
        # 检查邮箱配置
        email_type = self.settings.get("auto_register_email_type", "temp")
        
        # 打印邮箱类型，帮助调试
        self.add_log(f"当前邮箱类型: {email_type}", "info")
        
        if email_type == "temp":
            # 检查临时邮箱配置
            temp_mail = self.settings.get("auto_register_temp_mail", "")
            temp_mail_epin = self.settings.get("auto_register_temp_mail_epin", "")
            
            if not temp_mail or not temp_mail_epin:
                self.add_log("临时邮箱配置不完整，请先在设置中完成配置", "error")
                self.show_toast("临时邮箱配置不完整", True)
                return False
        elif email_type == "imap":
            # 检查IMAP邮箱配置
            imap_server = self.settings.get("auto_register_imap_server", "")
            imap_port = self.settings.get("auto_register_imap_port", "")
            imap_user = self.settings.get("auto_register_imap_user", "")
            imap_pass = self.settings.get("auto_register_imap_pass", "")
            
            if not imap_server or not imap_port or not imap_user or not imap_pass:
                self.add_log("IMAP邮箱配置不完整，请先在设置中完成配置", "error")
                self.show_toast("IMAP邮箱配置不完整", True)
                return False
        else:
            # 未知邮箱类型
            self.add_log(f"未知的邮箱类型: {email_type}，请检查配置", "error")
            self.show_toast("邮箱类型配置错误", True)
            return False
                
        return True
        
    def keyPressEvent(self, event):
        """处理键盘事件"""
        # 检查是否按下ESC键
        if event.key() == Qt.Key.Key_Escape:
            # 如果注册已完成，直接关闭对话框，不显示任何提示
            if self.registration_completed:
                self.safe_close()
                return
                
            # 如果注册未完成，才显示提示消息
            self.show_toast("正在停止自动注册流程...", False, 3000)
            # 终止工作线程
            if self.worker and self.worker.isRunning():
                self.add_log("用户按下ESC键，正在停止自动注册流程...", "warning")
                self.worker.stop()
            # 安全关闭对话框
            self.safe_close()
        else:
            # 其他键按下，传递给父类处理
            super().keyPressEvent(event)
            
    def closeEvent(self, event):
        """关闭事件处理 - 非阻塞方式"""
        # 如果注册已完成，直接关闭对话框，不需要显示终止提示
        if self.registration_completed:
            event.accept()
            return
            
        # 检查是否有正在运行的工作线程
        if self.worker and self.worker.isRunning():
            # 设置停止标志以防止创建新线程
            self.worker._stop_flag = True
            
            # 向用户显示Toast提示
            # 获取主窗口作为toast的父窗口
            main_window = self.parent()
            while main_window and not main_window.isWindow():
                main_window = main_window.parent()
                
            if main_window is None:
                main_window = self
                
            # 使用主窗口的toast系统显示提示
            if hasattr(main_window, 'toast_queue') and main_window.toast_queue:
                main_window.toast_queue.add_message("自动注册已取消，程序已终止", 3000, False)
            
            # 优先使用工作线程的stop方法，而不是直接terminate
            self.add_log("正在停止自动注册流程...", "warning")
            
            # 不要阻塞GUI线程，直接停止工作线程
            self.worker.stop()
            
            # 使用安全的方式处理线程和定时器，避免线程安全问题
            # 在主线程中创建计时器
            # 首先断开所有之前可能存在的计时器连接
            if hasattr(self, 'check_timer') and self.check_timer:
                try:
                    # 在主线程中安全停止计时器
                    self.check_timer.stop()
                    # 断开所有信号连接
                    try:
                        self.check_timer.timeout.disconnect()
                    except:
                        pass  # 忽略断开不存在连接的错误
                except:
                    pass
            
            # 创建新的计时器
            self.check_timer = QTimer(self)
            self.check_timer.setSingleShot(True)
            self.check_timer.timeout.connect(self._force_terminate_if_needed)
            self.check_timer.start(500)  # 500毫秒后检查
            
            # A. 添加线程安全标记，在安全的情况下才操作Qt对象
            self.setAttribute(Qt.WidgetAttribute.WA_DeleteOnClose, True)
            
            # 因为我们要异步处理线程终止，所以在GUI上先接受关闭事件
            event.accept()
            return
        
        # 如果没有运行中的线程，直接接受关闭事件
        event.accept()
    
    def _force_terminate_if_needed(self):
        """检查工作线程是否还在运行，如果是则尝试更强力的方法"""
        # 检查worker是否仍在运行
        if self.worker and self.worker.isRunning():
            # 再次确保停止标志已设置
            self.worker._stop_flag = True
            
            # 防止线程安全问题：确保所有GUI相关操作在主线程中进行
            self.add_log("线程仍在运行，尝试强制终止...", "warning")
            
            # 尝试清理浏览器资源
            try:
                if hasattr(self.worker, 'browser_manager') and self.worker.browser_manager:
                    self.worker.browser_manager.quit()
                    self.worker.browser_manager = None
                    self.worker.browser = None
            except Exception as e:
                self.add_log(f"关闭浏览器时出错: {str(e)}", "error")
            
            # 重置模块状态
            try:
                import core.cursor_auto.cursor_pro_keep_alive as keep_alive
                # 优先使用新的模块重置功能
                if hasattr(keep_alive, 'reset_module_state'):
                    keep_alive.reset_module_state()
                    # self.add_log("强制终止: 已重置cursor_pro_keep_alive模块状态", "info")
                else:
                    # 回退到手动清理逻辑
                    if hasattr(keep_alive, 'email_handler') and keep_alive.email_handler:
                        # 先调用cleanup方法
                        if hasattr(keep_alive.email_handler, 'cleanup'):
                            try:
                                keep_alive.email_handler.cleanup()
                                self.add_log("强制终止: 已调用email_handler.cleanup()方法", "info")
                            except Exception as e:
                                self.add_log(f"强制终止: 调用email_handler.cleanup()方法出错: {str(e)}", "warning")
                        
                        # 设置停止标志
                        if hasattr(keep_alive.email_handler, 'set_should_stop'):
                            keep_alive.email_handler.set_should_stop(True)
                        # 清除引用
                        keep_alive.email_handler = None
                        self.add_log("强制终止: 已清理全局email_handler", "info")
            except Exception as e:
                self.add_log(f"强制终止: 清理全局email_handler时出错: {str(e)}", "warning")
            
            # 在主线程中安全操作定时器
            if hasattr(self, 'check_timer') and self.check_timer:
                # 断开当前连接
                try:
                    self.check_timer.timeout.disconnect()
                except:
                    pass
                
                # 重新连接到下一个处理函数
                self.check_timer.timeout.connect(self._kill_remaining_processes)
                self.check_timer.start(1000)  # 1秒后再次检查
            else:
                # 如果定时器不存在，创建一个新的
                self.check_timer = QTimer(self)
                self.check_timer.setSingleShot(True)
                self.check_timer.timeout.connect(self._kill_remaining_processes)
                self.check_timer.start(1000)
            
            # 安全终止线程
            try:
                self.worker.terminate()
            except:
                pass
            
            # 触发垃圾回收
            try:
                import gc
                gc.collect()
                self.add_log("强制终止: 已触发垃圾回收", "info")
            except Exception as e:
                self.add_log(f"强制终止: 触发垃圾回收时出错: {str(e)}", "warning")
        else:
            # 线程已终止，不需要进一步操作
            self.add_log("线程已正常终止", "success")
            
            # 最后一次确认全局模块状态已重置
            try:
                import core.cursor_auto.cursor_pro_keep_alive as keep_alive
                # 优先使用新的模块重置功能
                if hasattr(keep_alive, 'reset_module_state'):
                    keep_alive.reset_module_state()
                    # self.add_log("线程终止后: 已重置cursor_pro_keep_alive模块状态", "info")
                else:
                    # 回退到手动清理
                    if hasattr(keep_alive, 'email_handler') and keep_alive.email_handler:
                        # 调用cleanup方法
                        if hasattr(keep_alive.email_handler, 'cleanup'):
                            try:
                                keep_alive.email_handler.cleanup()
                            except Exception:
                                pass
                        keep_alive.email_handler = None
                        self.add_log("线程终止后: 确认清理全局email_handler", "info")
            except Exception:
                pass
            
            # 安全停止定时器
            if hasattr(self, 'check_timer') and self.check_timer:
                try:
                    self.check_timer.stop()
                    self.check_timer = None
                except:
                    pass
    
    def _kill_remaining_processes(self):
        """最终尝试，杀死所有可能的残留进程"""
        if self.worker and self.worker.isRunning():
            self.add_log("线程无法正常终止，强制关闭相关进程...", "error")
            
            # 最终清理全局email_handler
            try:
                import core.cursor_auto.cursor_pro_keep_alive as keep_alive
                # 优先使用模块重置函数
                if hasattr(keep_alive, 'reset_module_state'):
                    keep_alive.reset_module_state()
                    # self.add_log("紧急清理: 已重置cursor_pro_keep_alive模块状态", "info")
                else:
                    # 回退到手动清理方式
                    # 确保全局email_handler被彻底清除
                    if hasattr(keep_alive, 'email_handler') and keep_alive.email_handler:
                        # 先调用cleanup方法
                        if hasattr(keep_alive.email_handler, 'cleanup'):
                            try:
                                keep_alive.email_handler.cleanup()
                                self.add_log("紧急清理: 已调用email_handler.cleanup()方法", "info")
                            except Exception as e:
                                self.add_log(f"紧急清理: 调用email_handler.cleanup()方法出错: {str(e)}", "warning")
                        keep_alive.email_handler = None
                        self.add_log("紧急清理: 已重置全局email_handler", "info")
            except Exception as e:
                self.add_log(f"紧急清理全局变量时出错: {str(e)}", "warning")
            
            # 强制触发垃圾回收
            try:
                import gc
                gc.collect()
                self.add_log("紧急清理: 已触发垃圾回收", "info")
            except Exception as e:
                self.add_log(f"紧急清理: 触发垃圾回收时出错: {str(e)}", "warning")
                
            # 安全停止定时器
            if hasattr(self, 'check_timer') and self.check_timer:
                try:
                    self.check_timer.stop()
                    self.check_timer = None
                except:
                    pass
                
            # 最终尝试：杀死所有潜在的子进程
            try:
                import psutil
                current_process = psutil.Process()
                for child in current_process.children(recursive=True):
                    try:
                        child.kill()  # 使用kill()而不是terminate()，更强力
                    except:
                        pass
                        
                # 再次检查并尝试杀死所有Chrome相关进程
                for proc in psutil.process_iter(['name']):
                    if proc.info['name'] and ('chrome' in proc.info['name'].lower() or 
                                            'chromedriver' in proc.info['name'].lower()):
                        try:
                            proc.kill()
                        except:
                            pass
            except Exception as e:
                self.add_log(f"清理进程时出错: {str(e)}", "error")
                
            # 不再尝试检查
            self.check_timer.stop()
            self.check_timer = None

    def load_function_settings(self):
        """加载功能配置"""
        function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
        function_settings = {}
        
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(function_config_file), exist_ok=True)
            
            # 如果文件存在，读取配置
            if os.path.exists(function_config_file):
                with open(function_config_file, 'r', encoding='utf-8') as f:
                    function_settings = json.load(f)
            else:
                # 如果文件不存在，创建默认配置
                function_settings = {
                    "register_only": False,  # 默认为 False，即正常更新认证信息
                    "register_method": 1     # 默认为方案一
                }
                # 保存默认配置
                with open(function_config_file, 'w', encoding='utf-8') as f:
                    json.dump(function_settings, f, ensure_ascii=False, indent=2)
                
            # 记录当前配置的方案信息
            register_method = function_settings.get("register_method", 1)
            debug_info = "方案一" if register_method == 1 else "方案二"
            info(f"加载功能配置完成：自动注册将使用 {debug_info}")
            
            return function_settings
        except Exception as e:
            error(f"加载功能配置出错: {str(e)}")
            # 返回默认配置
            return {"register_only": False, "register_method": 1}
    
    # 添加一个新的方法，确保安全地销毁对象
    def __del__(self):
        """安全地销毁对象，防止线程相关崩溃"""
        try:
            # 确保停止所有计时器
            if hasattr(self, 'check_timer') and self.check_timer:
                try:
                    self.check_timer.stop()
                    # 断开所有连接
                    try:
                        self.check_timer.timeout.disconnect()
                    except:
                        pass
                except:
                    pass
                
            # 确保线程已终止
            if hasattr(self, 'worker') and self.worker:
                if self.worker.isRunning():
                    try:
                        self.worker._stop_flag = True
                        self.worker.terminate()
                        self.worker.wait(300)  # 等待最多300毫秒
                    except:
                        pass
        except:
            pass
            
    # 添加无线程关闭方法
    def safe_close(self):
        """安全地关闭对话框，确保线程已停止"""
        # 在关闭前停止任何运行中的线程
        if hasattr(self, 'worker') and self.worker and self.worker.isRunning():
            self.worker._stop_flag = True
            self.worker.stop()
            try:
                self.worker.wait(1000)  # 等待1秒
            except:
                pass
            
        # 断开计时器连接
        if hasattr(self, 'check_timer') and self.check_timer:
            try:
                self.check_timer.stop()
                self.check_timer = None
            except:
                pass
                
        # 调用Qt的close方法
        super().close()