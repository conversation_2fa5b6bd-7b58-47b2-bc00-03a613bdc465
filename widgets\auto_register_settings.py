#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
自动注册配置组件模块
提供自动注册功能的配置界面和相关组件
"""

import os
import json
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, QComboBox, 
    QLineEdit, QCheckBox, QFrame, QPushButton, QGridLayout,
    QSpacerItem, QSizePolicy, QTabWidget, QStackedWidget, QDialog,
    QGraphicsDropShadowEffect, QFileDialog, QMessageBox
)
from PySide6.QtCore import Qt, Signal, QTimer
from PySide6.QtGui import QFont, QColor
import logging
import sys

from widgets.dialog import StyledDialog
from widgets.styled_widgets import StyledFrame, StyledButton, StyledSwitch, StyledLineEdit, StyledComboBox, StyledPasswordLineEdit
from theme import Theme
from utils import get_app_data_dir
from logger import info, error

# 获取logger
logger = logging.getLogger(__name__)

class AutoRegisterSettings(QWidget):
    """自动注册配置组件"""
    
    settings_updated = Signal(dict) # Signal to emit when settings are updated
    toast_request = Signal(str, str)
    
    def __init__(self, parent=None):
        super().__init__(parent)
        
        # 设置文件路径
        self.settings_file = os.path.join(get_app_data_dir(), "settings.json")
        self.settings = self.load_settings()
        
        # 初始化UI
        self.init_ui()
        
    def create_setting_item(self, icon, title, description=None):
        """创建设置项 - 与偏好设置页面风格一致"""
        # 主布局
        main_layout = QVBoxLayout()
        main_layout.setSpacing(8)
        
        # 标题和控件布局
        title_layout = QHBoxLayout()
        title_layout.setSpacing(10)
        
        # 图标
        icon_label = QLabel(icon)
        icon_label.setFont(QFont(Theme.FONT_FAMILY, 14))
        title_layout.addWidget(icon_label)
        
        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"color: {Theme.TEXT_PRIMARY}; font-size: {Theme.FONT_SIZE_NORMAL};")
        title_layout.addWidget(title_label)
        
        title_layout.addStretch()
        
        # 控件区域
        control_layout = QHBoxLayout()
        control_layout.setSpacing(0)
        title_layout.addLayout(control_layout)
        
        main_layout.addLayout(title_layout)
        
        # 描述（如果有）
        if description:
            desc_layout = QHBoxLayout()
            desc_layout.setContentsMargins(24, 0, 0, 0)  # 左侧缩进，与图标对齐
            
            desc_label = QLabel(description)
            desc_label.setWordWrap(True)
            desc_label.setStyleSheet(f"font-size: {Theme.FONT_SIZE_SMALL}; color: {Theme.TEXT_SECONDARY}; background-color: transparent;")
            desc_layout.addWidget(desc_label)
            
            main_layout.addLayout(desc_layout)
        
        # 返回布局和控件区域，以便外部添加控件
        return {
            "main_layout": main_layout,
            "control_layout": control_layout
        }

    def init_ui(self):
        """初始化UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(20)  # 调整卡片之间的间距为20，与偏好设置一致
        
        # ===== 域名配置 =====
        domain_section = QWidget()
        domain_section.setObjectName("domainSection")
        domain_section.setStyleSheet(f"""
            #domainSection {{
                background-color: transparent;
                border-radius: 12px;
            }}
        """)
        domain_layout = QVBoxLayout(domain_section)
        domain_layout.setContentsMargins(20, 20, 20, 20)  # 调整内边距与偏好设置一致
        domain_layout.setSpacing(15)  # 调整内部间距与偏好设置一致
        
        # 创建域名设置项
        domain_setting = self.create_setting_item(
            "🌐",
            "域名",
            "设置好邮件路由规则的域名，多个请使用英文逗号分隔"
        )
        
        self.domain_input = StyledLineEdit()
        self.domain_input.setPlaceholderText("xx.com,xx.cn")
        self.domain_input.setText(self.settings.get("auto_register_domain", ""))
        self.domain_input.textChanged.connect(self.save_settings)
        self.domain_input.setMinimumHeight(32)  # 设置固定高度
        self.domain_input.setFixedWidth(300)  # 与邮箱类型选择框相同的宽度
        self.domain_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        domain_setting["control_layout"].addWidget(self.domain_input)
        domain_layout.addLayout(domain_setting["main_layout"])
        
        # ===== 邮箱配置 =====
        email_section = QWidget()
        email_section.setObjectName("emailSection")
        email_section.setStyleSheet(f"""
            #emailSection {{
                background-color: transparent;
                border-radius: 12px;
            }}
        """)
        email_section.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.MinimumExpanding)
        
        email_layout = QVBoxLayout(email_section)
        email_layout.setContentsMargins(20, 20, 20, 20)  # 调整内边距与偏好设置一致
        email_layout.setSpacing(15)  # 调整内部间距与偏好设置一致
        
        # 创建邮箱类型设置项
        email_type_setting = self.create_setting_item(
            "📧",
            "收件邮箱类型",
            "选择用于接收验证码的邮箱类型"
        )
        
        self.email_type_combo = StyledComboBox()
        self.email_type_combo.addItem("tempmail.plus 临时邮箱", "temp")
        self.email_type_combo.addItem("IMAP邮箱", "imap")
        self.email_type_combo.setMinimumHeight(32)  # 设置固定高度
        self.email_type_combo.setFixedWidth(300)  # 设置固定宽度，作为统一标准
        self.email_type_combo.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        # 根据设置选择默认值
        email_type = self.settings.get("auto_register_email_type", "temp")
        index = 0 if email_type == "temp" else 1
        self.email_type_combo.setCurrentIndex(index)
        self.email_type_combo.currentIndexChanged.connect(self.on_email_type_changed)
        
        email_type_setting["control_layout"].addWidget(self.email_type_combo)
        email_layout.addLayout(email_type_setting["main_layout"])
        
        # 确保所有输入框宽度与邮箱类型选择框一致，通过控制layout实现统一宽度
        
        # 添加间隔
        spacer = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        email_layout.addItem(spacer)
        
        # 添加一个堆叠小部件来容纳不同类型的邮箱配置
        self.email_stack = QStackedWidget()
        self.email_stack.setContentsMargins(0, 0, 0, 0)
        self.email_stack.setStyleSheet("background-color: transparent;")
        
        # ===== 临时邮箱配置 =====
        self.temp_mail_widget = QWidget()
        self.temp_mail_widget.setStyleSheet("background-color: transparent;")
        temp_mail_layout = QVBoxLayout(self.temp_mail_widget)
        temp_mail_layout.setContentsMargins(0, 0, 0, 0)
        temp_mail_layout.setSpacing(15)  # 调整内部间距与偏好设置一致
        
        # 临时邮箱地址
        temp_mail_address_setting = self.create_setting_item(
            "📩",
            "邮箱地址",
            "tempmail.plus 临时邮箱服务的完整邮箱地址"
        )
        
        self.temp_mail_address_input = StyledLineEdit()
        self.temp_mail_address_input.setPlaceholderText("<EMAIL>")
        self.temp_mail_address_input.setText(self.settings.get("auto_register_temp_mail", ""))
        self.temp_mail_address_input.textChanged.connect(self.save_settings)
        self.temp_mail_address_input.setMinimumHeight(32)  # 设置固定高度
        self.temp_mail_address_input.setFixedWidth(300)  # 与邮箱类型选择框相同的宽度
        self.temp_mail_address_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        temp_mail_address_setting["control_layout"].addWidget(self.temp_mail_address_input)
        temp_mail_layout.addLayout(temp_mail_address_setting["main_layout"])
        
        # 临时邮箱PIN码
        temp_mail_pin_setting = self.create_setting_item(
            "🔑",
            "PIN码",
            "tempmail.plus 临时邮箱服务的验证PIN码"
        )
        
        self.temp_mail_pin_input = StyledPasswordLineEdit()
        self.temp_mail_pin_input.setPlaceholderText("请输入临时邮箱的PIN码")
        self.temp_mail_pin_input.setText(self.settings.get("auto_register_temp_mail_epin", ""))
        self.temp_mail_pin_input.textChanged.connect(self.save_settings)
        self.temp_mail_pin_input.setMinimumHeight(32)  # 设置固定高度
        self.temp_mail_pin_input.setFixedWidth(300)  # 与邮箱类型选择框相同的宽度
        self.temp_mail_pin_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        temp_mail_pin_setting["control_layout"].addWidget(self.temp_mail_pin_input)
        temp_mail_layout.addLayout(temp_mail_pin_setting["main_layout"])
        
        # 添加弹性空间填充
        temp_mail_layout.addStretch(1)
        
        # ===== IMAP邮箱配置 =====
        self.imap_mail_widget = QWidget()
        self.imap_mail_widget.setStyleSheet("background-color: transparent;")
        imap_mail_layout = QVBoxLayout(self.imap_mail_widget)
        imap_mail_layout.setContentsMargins(0, 0, 0, 0)
        imap_mail_layout.setSpacing(15)  # 调整内部间距与偏好设置一致
        
        # IMAP服务器
        imap_server_setting = self.create_setting_item(
            "🖥️",
            "IMAP服务器地址",
            "IMAP邮箱服务器地址"
        )
        
        self.imap_server_input = StyledLineEdit()
        self.imap_server_input.setPlaceholderText("imap.qq.com")
        self.imap_server_input.setText(self.settings.get("auto_register_imap_server", "imap.qq.com"))
        self.imap_server_input.textChanged.connect(self.save_settings)
        self.imap_server_input.setMinimumHeight(32)  # 设置固定高度
        self.imap_server_input.setFixedWidth(300)  # 与邮箱类型选择框相同的宽度
        self.imap_server_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        imap_server_setting["control_layout"].addWidget(self.imap_server_input)
        imap_mail_layout.addLayout(imap_server_setting["main_layout"])
        
        # 添加每个设置项之间的间隔
        spacer1 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        imap_mail_layout.addItem(spacer1)
        
        # IMAP端口
        imap_port_setting = self.create_setting_item(
            "🔌",
            "IMAP端口",
            "IMAP服务器连接端口"
        )
        
        self.imap_port_input = StyledLineEdit()
        self.imap_port_input.setPlaceholderText("993")
        self.imap_port_input.setText(self.settings.get("auto_register_imap_port", "993"))
        self.imap_port_input.textChanged.connect(self.save_settings)
        self.imap_port_input.setMinimumHeight(32)  # 设置固定高度
        self.imap_port_input.setFixedWidth(80)  # 内容少，强制设置固定宽度
        self.imap_port_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        self.imap_port_input.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 设置文本居中对齐
        
        imap_port_setting["control_layout"].addWidget(self.imap_port_input)
        imap_mail_layout.addLayout(imap_port_setting["main_layout"])
        
        # 添加每个设置项之间的间隔
        spacer2 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        imap_mail_layout.addItem(spacer2)
        
        # IMAP用户名
        imap_user_setting = self.create_setting_item(
            "📩",
            "邮箱地址",
            "邮箱账号的完整地址"
        )
        
        self.imap_user_input = StyledLineEdit()
        self.imap_user_input.setPlaceholderText("<EMAIL>")
        self.imap_user_input.setText(self.settings.get("auto_register_imap_user", ""))
        self.imap_user_input.textChanged.connect(self.save_settings)
        self.imap_user_input.setMinimumHeight(32)  # 设置固定高度
        self.imap_user_input.setFixedWidth(300)  # 与邮箱类型选择框相同的宽度
        self.imap_user_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        imap_user_setting["control_layout"].addWidget(self.imap_user_input)
        imap_mail_layout.addLayout(imap_user_setting["main_layout"])
        
        # 添加每个设置项之间的间隔
        spacer3 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        imap_mail_layout.addItem(spacer3)
        
        # IMAP密码
        imap_pass_setting = self.create_setting_item(
            "🔒",
            "密码/授权码",
            "IMAP邮箱密码或应用专用密码"
        )
        
        self.imap_pass_input = StyledPasswordLineEdit()
        self.imap_pass_input.setPlaceholderText("邮箱密码或应用专用密码")
        self.imap_pass_input.setText(self.settings.get("auto_register_imap_pass", ""))
        self.imap_pass_input.textChanged.connect(self.save_settings)
        self.imap_pass_input.setMinimumHeight(32)  # 设置固定高度
        self.imap_pass_input.setFixedWidth(300)  # 与邮箱类型选择框相同的宽度
        self.imap_pass_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        imap_pass_setting["control_layout"].addWidget(self.imap_pass_input)
        imap_mail_layout.addLayout(imap_pass_setting["main_layout"])
        
        # 添加每个设置项之间的间隔
        spacer4 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        imap_mail_layout.addItem(spacer4)
        
        # IMAP收件箱
        imap_mailbox_setting = self.create_setting_item(
            "📁",
            "IMAP收件箱",
            "IMAP邮箱的收件箱名称"
        )
        
        self.imap_mailbox_input = StyledLineEdit()
        self.imap_mailbox_input.setPlaceholderText("通常为INBOX")
        self.imap_mailbox_input.setText(self.settings.get("auto_register_imap_dir", "INBOX"))
        self.imap_mailbox_input.textChanged.connect(self.save_settings)
        self.imap_mailbox_input.setMinimumHeight(32)  # 设置固定高度
        self.imap_mailbox_input.setFixedWidth(80)  # 内容少，强制设置固定宽度
        self.imap_mailbox_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        self.imap_mailbox_input.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 设置文本居中对齐
        
        imap_mailbox_setting["control_layout"].addWidget(self.imap_mailbox_input)
        imap_mail_layout.addLayout(imap_mailbox_setting["main_layout"])
        
        # 添加弹性空间填充
        imap_mail_layout.addStretch(1)
        
        # 添加到堆叠小部件
        self.email_stack.addWidget(self.temp_mail_widget)
        self.email_stack.addWidget(self.imap_mail_widget)
        
        # 设置堆叠小部件的大小策略
        self.email_stack.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
        
        # 根据内容类型动态设置高度
        if index == 0:  # 临时邮箱 - 两个字段
            self.email_stack.setFixedHeight(125)  # 保持原高度
        else:  # IMAP邮箱 - 五个字段
            self.email_stack.setFixedHeight(300)  # 保持原高度，确保足够容纳带间隔的IMAP控件
            
        # 设置当前显示的邮箱类型
        self.email_stack.setCurrentIndex(index)
        
        # 将堆叠小部件直接添加到邮箱布局
        email_layout.addWidget(self.email_stack)
        
        # ===== 验证设置 =====
        verification_section = QWidget()
        verification_section.setObjectName("verificationSection")
        verification_section.setStyleSheet(f"""
            #verificationSection {{
                background-color: transparent;
                border-radius: 12px;
            }}
        """)
        verification_layout = QVBoxLayout(verification_section)
        verification_layout.setContentsMargins(20, 20, 20, 20)  # 调整内边距与偏好设置一致
        verification_layout.setSpacing(15)  # 调整内部间距与偏好设置一致
        
        # Turnstile验证超时
        turnstile_timeout_setting = self.create_setting_item(
            "⏱️",
            "Turnstile验证超时",
            "Turnstile人机验证的超时时间（秒）"
        )
        
        self.turnstile_timeout_input = StyledLineEdit()
        self.turnstile_timeout_input.setPlaceholderText("默认：20")
        self.turnstile_timeout_input.setText(self.settings.get("auto_register_turnstile_timeout", "20"))
        self.turnstile_timeout_input.textChanged.connect(self.save_settings)
        self.turnstile_timeout_input.setMinimumHeight(32)  # 设置固定高度
        self.turnstile_timeout_input.setFixedWidth(80)  # 内容少，强制设置固定宽度
        self.turnstile_timeout_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        self.turnstile_timeout_input.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 设置文本居中对齐
        
        turnstile_timeout_setting["control_layout"].addWidget(self.turnstile_timeout_input)
        verification_layout.addLayout(turnstile_timeout_setting["main_layout"])
        
        # 添加间隔
        spacer5 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        verification_layout.addItem(spacer5)
        
        # 验证码超时
        verification_timeout_setting = self.create_setting_item(
            "⏰",
            "验证码超时",
            "等待接收验证码邮件的超时时间（秒）"
        )
        
        self.verification_timeout_input = StyledLineEdit()
        self.verification_timeout_input.setPlaceholderText("默认：180")
        self.verification_timeout_input.setText(self.settings.get("auto_register_verification_timeout", "180"))
        self.verification_timeout_input.textChanged.connect(self.save_settings)
        self.verification_timeout_input.setMinimumHeight(32)  # 设置固定高度
        self.verification_timeout_input.setFixedWidth(80)  # 内容少，强制设置固定宽度
        self.verification_timeout_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        self.verification_timeout_input.setAlignment(Qt.AlignmentFlag.AlignCenter)  # 设置文本居中对齐
        
        verification_timeout_setting["control_layout"].addWidget(self.verification_timeout_input)
        verification_layout.addLayout(verification_timeout_setting["main_layout"])
        
        # 添加间隔
        spacer6 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        verification_layout.addItem(spacer6)

        # 手动过CF人机验证开关
        manual_cf_setting = self.create_setting_item(
            "🤖",
            "手动过CF人机验证",
            "开启后，遇到CF人机验证时会暂停并弹出按钮让用户手动处理"
        )

        self.manual_cf_switch = StyledSwitch()
        self.manual_cf_switch.setChecked(self.settings.get("auto_register_manual_cf", False))
        self.manual_cf_switch.stateChanged.connect(lambda state: self.save_settings())

        manual_cf_setting["control_layout"].addWidget(self.manual_cf_switch)
        verification_layout.addLayout(manual_cf_setting["main_layout"])

        # 添加间隔
        spacer6_1 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        verification_layout.addItem(spacer6_1)

        # 绕过绑卡开关
        bypass_card_setting = self.create_setting_item(
            "💳",
            "绕过绑卡直接注册普通账号",
            "开启后自动绕过绑卡注册普通账号，需要绑卡注册试用账号的请关闭该选项"
        )

        self.bypass_card_switch = StyledSwitch()
        self.bypass_card_switch.setChecked(self.settings.get("auto_register_bypass_card", True))  # 默认开启
        self.bypass_card_switch.stateChanged.connect(lambda state: self.save_settings())

        bypass_card_setting["control_layout"].addWidget(self.bypass_card_switch)
        verification_layout.addLayout(bypass_card_setting["main_layout"])

        # 添加间隔
        spacer6_2 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        verification_layout.addItem(spacer6_2)

        # 清理邮件开关
        clean_emails_setting = self.create_setting_item(
            "🧹",
            "自动清理所有Cursor邮件",
            "使用临时邮箱推荐开，IMAP慎重(不保证不会出现一些Bug)"
        )

        self.clean_emails_switch = StyledSwitch()
        self.clean_emails_switch.setChecked(self.settings.get("auto_register_clean_emails", False))
        self.clean_emails_switch.stateChanged.connect(lambda state: self.save_settings())

        clean_emails_setting["control_layout"].addWidget(self.clean_emails_switch)
        verification_layout.addLayout(clean_emails_setting["main_layout"])
        
        # 添加所有部分到主布局
        main_layout.addWidget(domain_section)
        main_layout.addWidget(email_section)
        main_layout.addWidget(verification_section)
        
        # ===== 高级设置 =====
        advanced_section = QWidget()
        advanced_section.setObjectName("advancedSection")
        advanced_section.setStyleSheet(f"""
            #advancedSection {{
                background-color: transparent;
                border-radius: 12px;
            }}
        """)
        advanced_layout = QVBoxLayout(advanced_section)
        advanced_layout.setContentsMargins(20, 20, 20, 20)  # 调整内边距与偏好设置一致
        advanced_layout.setSpacing(15)  # 调整内部间距与偏好设置一致
        
        # 浏览器类型设置项
        browser_type_setting = self.create_setting_item(
            "🌍",
            "浏览器选择",
            "设置自动注册时启动哪个浏览器"
        )
        
        self.browser_type_combo = StyledComboBox()
        self.browser_type_combo.addItem("Chrome浏览器", "chrome")
        self.browser_type_combo.addItem("Edge浏览器", "edge")
        self.browser_type_combo.addItem("自定义路径(Chrome浏览器)", "custom_chrome")
        self.browser_type_combo.setMinimumHeight(32)  # 设置固定高度
        self.browser_type_combo.setFixedWidth(300)  # 统一宽度
        self.browser_type_combo.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        # 添加浏览器User-Agent设置项
        browser_ua_setting = self.create_setting_item(
            "🌐",
            "浏览器User-Agent",
            "设置浏览器User-Agent，留空时自动获取"
        )
        
        self.browser_ua_input = StyledLineEdit()
        self.browser_ua_input.setPlaceholderText("留空时自动获取")
        self.browser_ua_input.setText(self.settings.get("auto_register_browser_user_agent", ""))
        self.browser_ua_input.textChanged.connect(self.save_settings)
        self.browser_ua_input.setMinimumHeight(32)  # 设置固定高度
        self.browser_ua_input.setFixedWidth(300)  # 统一宽度
        self.browser_ua_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        # 根据设置选择默认值，默认使用Chrome
        browser_type = self.settings.get("auto_register_browser_type", "chrome")
        if browser_type == "chrome":
            index = 0
        elif browser_type == "edge":
            index = 1
        elif browser_type == "custom_chrome":
            index = 2
        else:
            index = 0
        self.browser_type_combo.setCurrentIndex(index)
        self.browser_type_combo.currentIndexChanged.connect(self.on_browser_type_changed)
        
        browser_type_setting["control_layout"].addWidget(self.browser_type_combo)
        advanced_layout.addLayout(browser_type_setting["main_layout"])
        
        # 添加间隔
        spacer_ua1 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        advanced_layout.addItem(spacer_ua1)
        
        # 添加UA设置项到高级设置布局
        browser_ua_setting["control_layout"].addWidget(self.browser_ua_input)
        advanced_layout.addLayout(browser_ua_setting["main_layout"])
        
        # 创建一个堆叠小部件来容纳浏览器路径选择
        self.browser_path_stack = QStackedWidget()
        self.browser_path_stack.setContentsMargins(0, 0, 0, 0)
        self.browser_path_stack.setStyleSheet("background-color: transparent;")
        
        # 创建空白页面
        self.empty_widget = QWidget()
        self.empty_widget.setStyleSheet("background-color: transparent;")
        
        # 创建浏览器路径输入区域
        self.browser_path_widget = QWidget()
        self.browser_path_widget.setStyleSheet("background-color: transparent;")
        browser_path_layout = QVBoxLayout(self.browser_path_widget)
        browser_path_layout.setContentsMargins(0, 5, 0, 0)  # 添加顶部间距
        
        # 浏览器路径输入框和浏览按钮
        path_input_layout = QHBoxLayout()
        path_input_layout.setContentsMargins(0, 0, 0, 0)
        path_input_layout.setSpacing(10)
        
        self.browser_path_input = StyledLineEdit()
        # 根据系统设置不同的示例提示路径
        if sys.platform == "win32":
            # Windows系统
            placeholder_path = "C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe"
        elif sys.platform == "darwin":
            # macOS系统
            placeholder_path = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
        else:
            # Linux系统
            placeholder_path = "/usr/bin/google-chrome"
        
        self.browser_path_input.setPlaceholderText(placeholder_path)
        self.browser_path_input.setText(self.settings.get("auto_register_custom_chrome_path", ""))
        self.browser_path_input.textChanged.connect(self.save_settings)
        self.browser_path_input.setMinimumHeight(32)  # 设置固定高度
        # 移除固定宽度，允许自动扩展
        self.browser_path_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        self.browser_path_browse_btn = QPushButton("浏览...")
        self.browser_path_browse_btn.setMinimumHeight(32)  # 设置固定高度
        self.browser_path_browse_btn.setFixedWidth(80)  # 设置固定宽度
        self.browser_path_browse_btn.clicked.connect(self.browse_chrome_path)
        
        path_input_layout.addWidget(self.browser_path_input, 1)  # 添加伸缩因子1，使输入框填满可用空间
        path_input_layout.addWidget(self.browser_path_browse_btn, 0)  # 伸缩因子0，保持原大小
        
        browser_path_layout.addLayout(path_input_layout)
        
        # 说明文字
        path_hint_label = QLabel("提示: 请指定Chrome浏览器可执行文件的完整路径")
        path_hint_label.setStyleSheet(f"color: {Theme.TEXT_SECONDARY}; font-size: {Theme.FONT_SIZE_SMALL}; margin-top: 5px;")
        browser_path_layout.addWidget(path_hint_label)
        
        # 添加到堆叠小部件
        self.browser_path_stack.addWidget(self.empty_widget)  # 索引0 - 空白页
        self.browser_path_stack.addWidget(self.browser_path_widget)  # 索引1 - 浏览器路径页
        
        # 根据当前选择的浏览器类型设置当前索引
        current_index = 1 if browser_type == "custom_chrome" else 0
        self.browser_path_stack.setCurrentIndex(current_index)
        
        # 根据当前页面动态设置高度
        if current_index == 0:  # 空白页
            self.browser_path_stack.setFixedHeight(0)  # 无需空间
        else:  # 浏览器路径页
            self.browser_path_stack.setFixedHeight(80)  # 足够显示内容的高度
        
        # 添加到高级设置布局中
        advanced_layout.addWidget(self.browser_path_stack)
        
        # 添加间隔
        spacer7 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        advanced_layout.addItem(spacer7)
        
        # 代理地址设置项
        proxy_setting = self.create_setting_item(
            "🌐",
            "代理地址",
            "设置浏览器代理 留空不启用"
        )
        
        self.proxy_input = StyledLineEdit()
        self.proxy_input.setPlaceholderText("http://127.0.0.1:7890")
        self.proxy_input.setText(self.settings.get("auto_register_proxy", ""))
        self.proxy_input.textChanged.connect(self.save_settings)
        self.proxy_input.setMinimumHeight(32)  # 设置固定高度
        self.proxy_input.setFixedWidth(300)  # 统一宽度
        self.proxy_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        proxy_setting["control_layout"].addWidget(self.proxy_input)
        advanced_layout.addLayout(proxy_setting["main_layout"])
        
        # 添加间隔
        spacer8 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        advanced_layout.addItem(spacer8)
        
        # 无头模式设置项
        headless_setting = self.create_setting_item(
            "🖥️",
            "无头模式",
            "启用后浏览器将在后台运行，不显示界面"
        )
        
        self.headless_switch = StyledSwitch()
        self.headless_switch.setChecked(self.settings.get("auto_register_headless", False))
        self.headless_switch.stateChanged.connect(lambda state: self.save_settings())
        
        headless_setting["control_layout"].addWidget(self.headless_switch)
        advanced_layout.addLayout(headless_setting["main_layout"])
        
        # 添加间隔
        spacer9 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        advanced_layout.addItem(spacer9)
        
        # 验证码正则表达式设置项
        regex_setting = self.create_setting_item(
            "🔍",
            "验证码正则表达式",
            "\\d写成\\\\d, 否则将导致正则表达式无法正确解析 留空使用默认表达式"
        )
        
        self.verification_regex_input = StyledLineEdit()
        self.verification_regex_input.setPlaceholderText("(?<![@.])\\b\\d{6}\\b(?!\\.[a-z]+)")
        self.verification_regex_input.setText(self.settings.get("auto_register_verification_pattern", ""))
        self.verification_regex_input.textChanged.connect(self.save_settings)
        self.verification_regex_input.setMinimumHeight(32)  # 设置固定高度
        self.verification_regex_input.setFixedWidth(300)  # 统一宽度
        self.verification_regex_input.setStyleSheet(f"""
            padding: 0px 8px;  /* 添加左右内边距 */
        """)
        
        regex_setting["control_layout"].addWidget(self.verification_regex_input)
        advanced_layout.addLayout(regex_setting["main_layout"])
        
        # 添加间隔
        spacer10 = QSpacerItem(20, 15, QSizePolicy.Policy.Minimum, QSizePolicy.Policy.Fixed)
        advanced_layout.addItem(spacer10)
        
        # 显示验证码邮件内容设置项
        show_email_setting = self.create_setting_item(
            "👁️",
            "显示验证码邮件内容",
            "启用后将显示验证码邮件的完整内容，便于调试"
        )
        
        self.show_email_content_switch = StyledSwitch()
        self.show_email_content_switch.setChecked(self.settings.get("auto_register_show_email_content", False))
        self.show_email_content_switch.stateChanged.connect(lambda state: self.save_settings())
        
        show_email_setting["control_layout"].addWidget(self.show_email_content_switch)
        advanced_layout.addLayout(show_email_setting["main_layout"])
        
        # 添加高级设置部分到主布局
        main_layout.addWidget(advanced_section)
        
    def on_email_type_changed(self, index):
        """邮箱类型改变处理函数"""
        # 获取旧类型
        old_email_type = self.settings.get("auto_register_email_type", "temp")
        new_email_type = "temp" if index == 0 else "imap"
        
        # 只有当类型确实改变时才执行操作
        if old_email_type != new_email_type:
            # 根据内容类型动态设置高度
            if index == 0:  # 临时邮箱 - 两个字段
                self.email_stack.setFixedHeight(125)  # 保持原高度
            else:  # IMAP邮箱 - 五个字段
                self.email_stack.setFixedHeight(300)  # 保持原高度，确保足够容纳带间隔的IMAP控件
                
            # 切换堆叠小部件的当前索引
            self.email_stack.setCurrentIndex(index)
            
            # 强制立即调整布局
            QTimer.singleShot(0, self.window().adjustSize)
            
            # 更新设置
            self.settings["auto_register_email_type"] = new_email_type
            info(f"自动注册配置已更新: 邮箱类型已更改为 {new_email_type}")
            
            # 立即保存到文件，确保配置被写入
            try:
                # 加载完整设置文件
                all_settings = {}
                if os.path.exists(self.settings_file):
                    with open(self.settings_file, 'r', encoding='utf-8') as f:
                        all_settings = json.load(f)
                
                # 更新邮箱类型设置
                all_settings["auto_register_email_type"] = new_email_type
                
                # 保存到文件
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    json.dump(all_settings, f, ensure_ascii=False, indent=2)
                
                info(f"邮箱类型已保存到配置文件: {new_email_type}")
            except Exception as e:
                error(f"保存邮箱类型设置时出错: {str(e)}")
        else:
            # 类型没有变化，只切换视图不显示通知
            self.email_stack.setCurrentIndex(index)
        
    def on_browser_type_changed(self, index):
        """浏览器类型改变处理函数"""
        # 获取旧类型
        old_browser_type = self.settings.get("auto_register_browser_type", "chrome")
        
        # 获取新类型
        new_browser_type = self.browser_type_combo.currentData()
        
        # 根据选择切换堆叠小部件的当前页面
        current_index = 1 if new_browser_type == "custom_chrome" else 0
        self.browser_path_stack.setCurrentIndex(current_index)
        
        # 根据当前页面动态设置高度
        if current_index == 0:  # 空白页
            self.browser_path_stack.setFixedHeight(0)  # 无需空间
        else:  # 浏览器路径页
            self.browser_path_stack.setFixedHeight(80)  # 足够显示内容的高度
        
        # 如果切换到自定义路径，确保路径输入框获得焦点
        if new_browser_type == "custom_chrome":
            QTimer.singleShot(100, self.browser_path_input.setFocus)
        
        # 只有当类型确实改变时才更新设置和保存
        if old_browser_type != new_browser_type:
            # 更新设置
            self.settings["auto_register_browser_type"] = new_browser_type
            self.save_settings()
            
            info(f"自动注册配置已更新: 浏览器类型已更改为 {new_browser_type}")
    
    def browse_chrome_path(self):
        """浏览选择Chrome浏览器路径"""
        # 获取初始目录
        initial_dir = ""
        if sys.platform == "win32":
            # Windows平台
            program_files = os.environ.get('PROGRAMFILES', 'C:\\Program Files')
            initial_dir = os.path.join(program_files, 'Google', 'Chrome', 'Application')
        elif sys.platform == "darwin":
            # macOS平台
            initial_dir = '/Applications'
        else:
            # Linux平台
            initial_dir = '/usr/bin'
        
        # 文件筛选器
        if sys.platform == "win32":
            file_filter = "可执行文件 (*.exe)"
        else:
            file_filter = "所有文件 (*)"
        
        # 打开文件对话框
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Chrome浏览器可执行文件",
            initial_dir,
            file_filter
        )
        
        # 如果用户选择了文件
        if file_path:
            self.browser_path_input.setText(file_path)
            self.save_settings()
            
            info(f"已设置自定义Chrome浏览器路径: {file_path}")
        
    def load_settings(self):
        """从settings.json加载设置"""
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                error(f"加载设置文件出错: {str(e)}")
                
        return {}
        
    def save_settings(self):
        """保存设置到settings.json并记录日志"""
        try:
            # 获取修改前的设置的副本，用于比较变化
            old_settings = self.settings.copy() if hasattr(self, 'settings') else {}
            
            # 更新前先备份当前设置
            current_settings = {}
            for key in self.settings:
                current_settings[key] = self.settings.get(key)
            
            # 更新设置字典 - 从UI控件获取最新值
            self.settings["auto_register_domain"] = self.domain_input.text().strip()
            
            # 确保从下拉框获取正确的邮箱类型
            email_type_index = self.email_type_combo.currentIndex()
            self.settings["auto_register_email_type"] = "temp" if email_type_index == 0 else "imap"
            
            self.settings["auto_register_temp_mail"] = self.temp_mail_address_input.text().strip()
            self.settings["auto_register_temp_mail_epin"] = self.temp_mail_pin_input.text().strip()
            self.settings["auto_register_imap_server"] = self.imap_server_input.text().strip()
            self.settings["auto_register_imap_port"] = self.imap_port_input.text().strip()
            self.settings["auto_register_imap_user"] = self.imap_user_input.text().strip()
            self.settings["auto_register_imap_pass"] = self.imap_pass_input.text().strip()
            self.settings["auto_register_imap_dir"] = self.imap_mailbox_input.text().strip()
            
            # 保存验证设置
            self.settings["auto_register_turnstile_timeout"] = self.turnstile_timeout_input.text().strip()
            self.settings["auto_register_verification_timeout"] = self.verification_timeout_input.text().strip()
            self.settings["auto_register_manual_cf"] = self.manual_cf_switch.isChecked()
            self.settings["auto_register_bypass_card"] = self.bypass_card_switch.isChecked()
            self.settings["auto_register_clean_emails"] = self.clean_emails_switch.isChecked()
            
            # 保存高级设置
            self.settings["auto_register_proxy"] = self.proxy_input.text().strip()
            self.settings["auto_register_headless"] = self.headless_switch.isChecked()
            self.settings["auto_register_browser_type"] = self.browser_type_combo.currentData()
            self.settings["auto_register_custom_chrome_path"] = self.browser_path_input.text().strip()
            self.settings["auto_register_browser_user_agent"] = self.browser_ua_input.text().strip()
            self.settings["auto_register_verification_pattern"] = self.verification_regex_input.text().strip()
            self.settings["auto_register_show_email_content"] = self.show_email_content_switch.isChecked()
            
            # 检查是否有变更
            has_changes = False
            changed_keys = []
            
            for key, new_value in self.settings.items():
                if key not in old_settings or old_settings.get(key) != new_value:
                    has_changes = True
                    changed_keys.append(key)
                    
                    # 特殊处理密码字段，不显示具体内容
                    if "pass" in key.lower():
                        if key not in old_settings or not old_settings.get(key):
                            info(f"自动注册配置已更新: {key} = [已设置新密码]")
                        elif old_settings.get(key) != new_value:
                            info(f"自动注册配置已更新: {key} = [密码已修改]")
                    else:
                        info(f"自动注册配置已更新: {key} = {new_value}")
            
            # 即使没有变更，也强制保存确保邮箱类型正确
            # 加载完整设置文件
            all_settings = {}
            if os.path.exists(self.settings_file):
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    all_settings = json.load(f)
            
            # 更新自动注册相关设置
            all_settings.update(self.settings)
            
            # 保存到文件
            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(all_settings, f, ensure_ascii=False, indent=2)
                
            # 发出设置更改信号
            self.settings_updated.emit(self.settings)
            
            # 记录日志
            if has_changes:
                info(f"自动注册设置已保存到文件，变更项: {', '.join(changed_keys)}")
            
        except Exception as e:
            error(f"保存设置出错: {str(e)}")
        
    def log_setting_change(self, key, value, is_sensitive=False):
        """记录设置变更到日志"""
        if is_sensitive:
            info(f"自动注册配置已更新: {key} = [敏感信息已隐藏]")
        else:
            info(f"自动注册配置已更新: {key} = {value}") 