"""
颜色和样式主题模块
定义应用程序中使用的所有颜色、字体、间距和其他视觉风格元素
"""

class Theme:
    # 主色
    PRIMARY = "#121317"  # 主背景色（深黑色）
    SECONDARY = "#1A1D23"  # 次要背景色（稍浅的黑色）
    ACCENT = "#2B9D7C"  # 暗绿色强调色（更暗的绿色）
    ACCENT_HOVER = "#34B892"  # 悬停时的强调色
    ACCENT_PRESSED = "#24856A"  # 按下时的强调色（更深的绿色）
    TEXT_PRIMARY = "#FFFFFF"  # 主要文本颜色
    TEXT_SECONDARY = "#9CA2AE"  # 次要文本颜色
    
    # 状态颜色
    SUCCESS = "#2B9D7C"  # 成功状态（暗绿色）
    WARNING = "#CBAF67"  # 警告状态（暗黄色）
    ERROR = "#BC4A59"  # 错误状态（暗红色）
    INFO = "#4A90E2"    # 信息状态（蓝色）
    GOLD = "#FFD700"    # 金色（用于高级用户显示）
    
    # 其他颜色
    CARD_BG = "#1E2128"  # 卡片背景
    BORDER = "#2A2E36"  # 边框颜色
    BORDER_COLOR = BORDER  # 边框颜色别名
    HOVER = "#252830"  # 悬停颜色
    SELECTION = "#2B9D7C"  # 选中颜色
    DISABLED = "#3A3E47"  # 禁用状态颜色
    
    # 卡片层级
    CARD_LEVEL_1 = "#1A1D23"  # 一级卡片
    CARD_LEVEL_2 = "#21252D"  # 二级卡片
    CARD_LEVEL_3 = "#282C36"  # 三级卡片
    
    # 窗口控制按钮
    WINDOW_CLOSE = "#BC4A59"
    WINDOW_CLOSE_HOVER = "#D04A57"
    WINDOW_MINIMIZE = "#CBAF67"
    WINDOW_MINIMIZE_HOVER = "#E8D28D"
    WINDOW_MAXIMIZE = "#2B9D7C"
    WINDOW_MAXIMIZE_HOVER = "#34B892"
    
    # 毛玻璃效果
    GLASS_BG = "rgba(30, 33, 40, 0.75)"  # 半透明背景
    GLASS_BORDER = "rgba(60, 63, 70, 0.35)"  # 半透明边框
    GLASS_SHADOW = "rgba(0, 0, 0, 0.2)"  # 阴影颜色
    
    # 渐变
    GRADIENT_START = "#121317"
    GRADIENT_END = "#1A1D23"
    
    # 字体
    FONT_FAMILY = "'Segoe UI', 'Microsoft YaHei UI', 'SF Pro Display', 'Helvetica Neue', Arial, sans-serif"
    FONT_SIZE_SMALL = "12px"
    FONT_SIZE_NORMAL = "14px"
    FONT_SIZE_LARGE = "16px"
    FONT_SIZE_TITLE = "18px"
    FONT_SIZE_HEADER = "24px"
    
    # 圆角
    BORDER_RADIUS = "14px"
    BORDER_RADIUS_SMALL = "10px"
    
    # 间距
    SPACING_SMALL = "8px"
    SPACING_MEDIUM = "16px"
    SPACING_LARGE = "24px"
    
    # 动画时间 - 用于QPropertyAnimation
    ANIMATION_FAST = 150  # ms
    ANIMATION_NORMAL = 250  # ms 
    ANIMATION_SLOW = 350  # ms
    
    # 阴影
    SHADOW_SMALL = "0 4px 8px rgba(0, 0, 0, 0.15)"
    SHADOW_MEDIUM = "0 6px 12px rgba(0, 0, 0, 0.2)"
    SHADOW_LARGE = "0 8px 16px rgba(0, 0, 0, 0.25)"
    
    # 卡片悬停提升
    CARD_LIFT = "transform: translateY(-2px);"
    
    # 交互动画
    # 已移除不支持的CSS属性
    # TRANSITION_NORMAL 和 TRANSITION_FAST