#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
订阅查询对话框模块
提供订阅状态查询和取消功能
"""

import webbrowser
from datetime import datetime
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton, 
    QFrame, QScrollArea, QSpacerItem, QSizePolicy, QProgressBar
)
from PySide6.QtCore import Qt, QThread, Signal, QTimer
from PySide6.QtGui import QFont

from theme import Theme
from logger import info, error, warning
from widgets.dialog import StyledDialog
from account.subscription_manager import CursorSubscriptionManager


class SubscriptionWorker(QThread):
    """订阅查询工作线程"""
    
    # 信号定义
    status_fetched = Signal(dict)  # 订阅状态获取完成
    cancel_completed = Signal(bool)  # 取消订阅完成
    error_occurred = Signal(str)  # 发生错误
    
    def __init__(self, account_data, operation='status'):
        super().__init__()
        self.account_data = account_data
        self.operation = operation  # 'status' 或 'cancel'
        self.manager = CursorSubscriptionManager(account_data)
    
    def run(self):
        """执行订阅操作"""
        try:
            if self.operation == 'status':
                # 查询订阅状态
                result = self.manager.view_subscription_status_flow()
                if result:
                    self.status_fetched.emit(result)
                else:
                    self.error_occurred.emit("获取订阅状态失败")
            elif self.operation == 'cancel':
                # 取消订阅
                result = self.manager.cancel_subscription_flow(refund=False)
                self.cancel_completed.emit(result)
        except Exception as e:
            self.error_occurred.emit(str(e))


class SubscriptionDialog(QDialog):
    """订阅查询对话框"""
    
    def __init__(self, account_data, main_window, parent=None):
        super().__init__(parent)
        self.account_data = account_data
        self.main_window = main_window
        self.email = account_data.get("email", "")
        self.subscription_data = None
        self.worker = None
        
        # 创建对话框
        self.dialog = StyledDialog(parent or main_window, f"订阅查询 - {self.email}", width=600)
        self._setup_ui()
        
        # 自动开始查询
        QTimer.singleShot(100, self._start_status_query)
    
    def _setup_ui(self):
        """设置UI"""
        # 创建主容器
        main_container = QFrame()
        main_container.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_1};
                border-radius: {Theme.BORDER_RADIUS};
            }}
        """)
        
        main_layout = QVBoxLayout(main_container)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)
        
        # 标题
        title_label = QLabel(f"📊 {self.email} 的订阅状态")
        title_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_TITLE};
            font-weight: bold;
            margin-bottom: 10px;
        """)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        main_layout.addWidget(title_label)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet(f"""
            QScrollArea {{
                border: none;
                background-color: transparent;
            }}
            QScrollBar:vertical {{
                border: none;
                background: transparent;
                width: 12px;
                margin: 2px;
            }}
            QScrollBar::handle:vertical {{
                background-color: #282C36;
                min-height: 30px;
                border-radius: 6px;
            }}
        """)
        
        # 内容容器
        self.content_widget = QFrame()
        self.content_widget.setStyleSheet("background: transparent;")
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(0, 0, 0, 0)
        self.content_layout.setSpacing(15)
        
        # 初始显示加载状态（先不调用，等按钮创建后再调用）
        
        scroll_area.setWidget(self.content_widget)
        main_layout.addWidget(scroll_area, 1)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.setSpacing(10)
        button_layout.addStretch()
        
        # 刷新按钮
        self.refresh_btn = QPushButton("🔄 刷新状态")
        self.refresh_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.refresh_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.INFO};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: #4A9EFF;
            }}
            QPushButton:pressed {{
                background-color: {Theme.INFO};
                opacity: 0.8;
            }}
            QPushButton:disabled {{
                background-color: #555;
                color: #999;
            }}
        """)
        self.refresh_btn.clicked.connect(self._start_status_query)
        button_layout.addWidget(self.refresh_btn)
        
        # 取消订阅按钮
        self.cancel_btn = QPushButton("❌ 取消订阅")
        self.cancel_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.cancel_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ERROR};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: #C53030;
            }}
            QPushButton:pressed {{
                background-color: {Theme.ERROR};
                opacity: 0.8;
            }}
            QPushButton:disabled {{
                background-color: #555;
                color: #999;
            }}
        """)
        self.cancel_btn.clicked.connect(self._start_cancel_subscription)
        self.cancel_btn.setEnabled(False)
        button_layout.addWidget(self.cancel_btn)
        
        # 打开Stripe URL按钮
        self.stripe_btn = QPushButton("🌐 打开Stripe页面")
        self.stripe_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.stripe_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.ACCENT};
                color: white;
                border: none;
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-weight: bold;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 120px;
            }}
            QPushButton:hover {{
                background-color: {Theme.ACCENT_HOVER};
            }}
            QPushButton:pressed {{
                background-color: {Theme.ACCENT};
                opacity: 0.8;
            }}
            QPushButton:disabled {{
                background-color: #555;
                color: #999;
            }}
        """)
        self.stripe_btn.clicked.connect(self._open_stripe_url)
        self.stripe_btn.setEnabled(False)
        button_layout.addWidget(self.stripe_btn)
        
        # 关闭按钮
        close_btn = QPushButton("关闭")
        close_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        close_btn.setStyleSheet(f"""
            QPushButton {{
                background-color: {Theme.CARD_LEVEL_2};
                color: {Theme.TEXT_PRIMARY};
                border: 1px solid {Theme.BORDER};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 8px 15px;
                font-size: {Theme.FONT_SIZE_NORMAL};
                min-width: 80px;
            }}
            QPushButton:hover {{
                border: 1px solid {Theme.ACCENT};
                color: {Theme.ACCENT};
            }}
            QPushButton:pressed {{
                background-color: #1A1D23;
            }}
        """)
        close_btn.clicked.connect(self.dialog.accept)
        button_layout.addWidget(close_btn)
        
        main_layout.addLayout(button_layout)
        self.dialog.addWidget(main_container)

        # 现在按钮已创建，可以显示加载状态
        self._show_loading_state()
    
    def _show_loading_state(self):
        """显示加载状态"""
        # 清除现有内容
        self._clear_content()
        
        # 加载指示器
        loading_frame = QFrame()
        loading_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 20px;
            }}
        """)
        
        loading_layout = QVBoxLayout(loading_frame)
        loading_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        loading_label = QLabel("🔄 正在查询订阅状态...")
        loading_label.setStyleSheet(f"""
            color: {Theme.ACCENT};
            font-size: {Theme.FONT_SIZE_LARGE};
            font-weight: bold;
        """)
        loading_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        loading_layout.addWidget(loading_label)
        
        # 进度条
        progress_bar = QProgressBar()
        progress_bar.setRange(0, 0)  # 无限进度条
        progress_bar.setStyleSheet(f"""
            QProgressBar {{
                background-color: {Theme.CARD_LEVEL_1};
                border-radius: 5px;
                text-align: center;
                color: {Theme.TEXT_PRIMARY};
                font-weight: bold;
                height: 20px;
            }}
            QProgressBar::chunk {{
                background-color: {Theme.ACCENT};
                border-radius: 5px;
            }}
        """)
        loading_layout.addWidget(progress_bar)
        
        self.content_layout.addWidget(loading_frame)

        # 禁用按钮（如果按钮已创建）
        if hasattr(self, 'refresh_btn'):
            self.refresh_btn.setEnabled(False)
        if hasattr(self, 'cancel_btn'):
            self.cancel_btn.setEnabled(False)
        if hasattr(self, 'stripe_btn'):
            self.stripe_btn.setEnabled(False)
    
    def _clear_content(self):
        """清除内容区域"""
        while self.content_layout.count():
            item = self.content_layout.takeAt(0)
            if item.widget():
                item.widget().deleteLater()
    
    def _start_status_query(self):
        """开始查询订阅状态"""
        if self.worker and self.worker.isRunning():
            return
            
        self._show_loading_state()
        
        self.worker = SubscriptionWorker(self.account_data, 'status')
        self.worker.status_fetched.connect(self._on_status_fetched)
        self.worker.error_occurred.connect(self._on_error_occurred)
        self.worker.start()
    
    def _start_cancel_subscription(self):
        """开始取消订阅"""
        if not self.subscription_data or not self.subscription_data.get('is_cancelable'):
            self.main_window.show_toast("当前订阅无法取消", error=True)
            return
        
        # 确认对话框
        from widgets.dialog import StyledDialog
        confirmed = StyledDialog.showConfirmDialog(
            self,
            "确认取消订阅",
            f"确定要取消账户 {self.email} 的订阅吗？\n\n取消后将在当前周期结束时停止服务。",
            confirm_text="确认取消",
            confirm_color=Theme.ERROR
        )
        
        if not confirmed:
            return
        
        if self.worker and self.worker.isRunning():
            return
            
        self._show_loading_state()
        
        self.worker = SubscriptionWorker(self.account_data, 'cancel')
        self.worker.cancel_completed.connect(self._on_cancel_completed)
        self.worker.error_occurred.connect(self._on_error_occurred)
        self.worker.start()
    
    def _on_status_fetched(self, data):
        """订阅状态获取完成"""
        self.subscription_data = data
        self._display_subscription_status(data)
        
        # 启用按钮
        self.refresh_btn.setEnabled(True)
        self.cancel_btn.setEnabled(data.get('is_cancelable', False) and not data.get('is_scheduled_for_cancellation', False))
        self.stripe_btn.setEnabled(bool(data.get('stripe_session_url')))
    
    def _on_cancel_completed(self, success):
        """取消订阅完成"""
        if success:
            self.main_window.show_toast(f"账户 {self.email} 订阅取消成功！")
            # 重新查询状态
            QTimer.singleShot(1000, self._start_status_query)
        else:
            self.main_window.show_toast(f"账户 {self.email} 订阅取消失败", error=True)
            if hasattr(self, 'refresh_btn'):
                self.refresh_btn.setEnabled(True)
    
    def _on_error_occurred(self, error_msg):
        """发生错误"""
        self._clear_content()
        
        error_frame = QFrame()
        error_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 20px;
                border: 2px solid {Theme.ERROR};
            }}
        """)
        
        error_layout = QVBoxLayout(error_frame)
        error_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        error_label = QLabel(f"❌ 查询失败\n\n{error_msg}")
        error_label.setStyleSheet(f"""
            color: {Theme.ERROR};
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
        """)
        error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        error_layout.addWidget(error_label)
        
        self.content_layout.addWidget(error_frame)

        # 启用刷新按钮（如果按钮已创建）
        if hasattr(self, 'refresh_btn'):
            self.refresh_btn.setEnabled(True)
        if hasattr(self, 'cancel_btn'):
            self.cancel_btn.setEnabled(False)
        if hasattr(self, 'stripe_btn'):
            self.stripe_btn.setEnabled(False)
        
        self.main_window.show_toast(f"查询订阅状态失败: {error_msg}", error=True)

    def _display_subscription_status(self, data):
        """显示订阅状态"""
        self._clear_content()

        if not data.get('has_subscription'):
            # 没有订阅
            no_sub_frame = QFrame()
            no_sub_frame.setStyleSheet(f"""
                QFrame {{
                    background-color: {Theme.CARD_LEVEL_2};
                    border-radius: {Theme.BORDER_RADIUS_SMALL};
                    padding: 20px;
                }}
            """)

            no_sub_layout = QVBoxLayout(no_sub_frame)
            no_sub_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)

            no_sub_label = QLabel("ℹ️ 未找到订阅\n\n该账户当前没有活跃的订阅")
            no_sub_label.setStyleSheet(f"""
                color: {Theme.TEXT_SECONDARY};
                font-size: {Theme.FONT_SIZE_LARGE};
                font-weight: bold;
            """)
            no_sub_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            no_sub_layout.addWidget(no_sub_label)

            self.content_layout.addWidget(no_sub_frame)
            return

        # 有订阅，显示详细信息
        # 状态卡片
        status_frame = self._create_info_card("📊 订阅状态", self._get_status_info(data))
        self.content_layout.addWidget(status_frame)

        # 时间信息卡片
        time_frame = self._create_info_card("⏰ 时间信息", self._get_time_info(data))
        self.content_layout.addWidget(time_frame)

        # 产品信息卡片
        if data.get('product_info'):
            product_frame = self._create_info_card("💰 产品信息", self._get_product_info(data))
            self.content_layout.addWidget(product_frame)

        # 操作状态卡片
        action_frame = self._create_info_card("🛠️ 操作状态", self._get_action_info(data))
        self.content_layout.addWidget(action_frame)

    def _create_info_card(self, title, content):
        """创建信息卡片"""
        card_frame = QFrame()
        card_frame.setStyleSheet(f"""
            QFrame {{
                background-color: {Theme.CARD_LEVEL_2};
                border-radius: {Theme.BORDER_RADIUS_SMALL};
                padding: 15px;
                margin: 5px 0px;
            }}
        """)

        card_layout = QVBoxLayout(card_frame)
        card_layout.setContentsMargins(0, 0, 0, 0)
        card_layout.setSpacing(10)

        # 标题
        title_label = QLabel(title)
        title_label.setStyleSheet(f"""
            color: {Theme.TEXT_PRIMARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            font-weight: bold;
            margin-bottom: 5px;
        """)
        card_layout.addWidget(title_label)

        # 内容
        content_label = QLabel(content)
        content_label.setStyleSheet(f"""
            color: {Theme.TEXT_SECONDARY};
            font-size: {Theme.FONT_SIZE_NORMAL};
            line-height: 1.4;
        """)
        content_label.setWordWrap(True)
        card_layout.addWidget(content_label)

        return card_frame

    def _get_status_info(self, data):
        """获取状态信息"""
        status = data.get('status', 'unknown')
        subscription_id = data.get('subscription_id', 'N/A')

        # 取消状态
        is_scheduled_for_cancellation = data.get('is_scheduled_for_cancellation', False)
        if is_scheduled_for_cancellation:
            cancellation_status = "✅ 已取消"
        else:
            cancellation_status = "❌ 未取消"

        return f"""订阅续费状态: {cancellation_status}
订阅ID: {subscription_id}
状态: {status}"""

    def _get_time_info(self, data):
        """获取时间信息"""
        info_lines = []

        # 创建时间
        created = data.get('created')
        if created:
            created_date = datetime.fromtimestamp(created).strftime('%Y-%m-%d %H:%M:%S')
            info_lines.append(f"创建时间: {created_date}")

        # 试用信息
        trial_end = data.get('trial_end')
        if trial_end:
            trial_end_date = datetime.fromtimestamp(trial_end).strftime('%Y-%m-%d %H:%M:%S')
            now = datetime.now()
            trial_end_dt = datetime.fromtimestamp(trial_end)

            if now < trial_end_dt:
                days_left = (trial_end_dt - now).days
                info_lines.append(f"试用到期: {trial_end_date} (还剩 {days_left} 天)")
            else:
                info_lines.append(f"试用已结束: {trial_end_date}")

        # 计费周期信息
        current_period_end = data.get('current_period_end')
        if current_period_end:
            period_end_date = datetime.fromtimestamp(current_period_end).strftime('%Y-%m-%d %H:%M:%S')
            info_lines.append(f"当前周期结束: {period_end_date}")

        # 取消信息
        cancel_at = data.get('cancel_at')
        cancel_at_period_end = data.get('cancel_at_period_end', False)

        if cancel_at:
            cancel_date = datetime.fromtimestamp(cancel_at).strftime('%Y-%m-%d %H:%M:%S')
            info_lines.append(f"将于以下时间停止服务: {cancel_date}")
        elif cancel_at_period_end:
            info_lines.append("将在当前周期结束时停止服务")

        return '\n'.join(info_lines) if info_lines else "暂无时间信息"

    def _get_product_info(self, data):
        """获取产品信息"""
        product_info = data.get('product_info', [])
        if not product_info:
            return "暂无产品信息"

        info_lines = []
        for product in product_info:
            name = product.get('name', 'Unknown Product')
            price = product.get('price', 0)
            currency = product.get('currency', 'USD')
            interval = product.get('interval', 'month')
            quantity = product.get('quantity', 1)

            info_lines.append(f"产品: {name}")
            info_lines.append(f"价格: {price:.2f} {currency} / {interval}")
            info_lines.append(f"数量: {quantity}")
            info_lines.append("")  # 空行分隔

        return '\n'.join(info_lines).strip()

    def _get_action_info(self, data):
        """获取操作信息"""
        is_scheduled_for_cancellation = data.get('is_scheduled_for_cancellation', False)
        is_cancelable = data.get('is_cancelable', False)

        if is_scheduled_for_cancellation:
            return "✅ 订阅已取消，无需进一步操作"
        elif is_cancelable:
            return "⚠️ 可以取消订阅"
        else:
            return "ℹ️ 暂时无法取消"

    def _open_stripe_url(self):
        """打开Stripe URL"""
        if self.subscription_data and self.subscription_data.get('stripe_session_url'):
            url = self.subscription_data['stripe_session_url']
            try:
                webbrowser.open(url)
                info(f"已打开Stripe页面: {url}")
                self.main_window.show_toast("已在浏览器中打开Stripe页面")
            except Exception as e:
                error(f"打开Stripe页面失败: {str(e)}")
                self.main_window.show_toast("打开Stripe页面失败", error=True)
        else:
            self.main_window.show_toast("没有可用的Stripe页面链接", error=True)

    def exec(self):
        """显示对话框并执行其主循环"""
        return self.dialog.exec()
