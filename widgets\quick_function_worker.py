#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快捷一键功能工作线程模块
提供在后台执行多个功能的线程类，支持进度报告和中断
"""

import os
import sys
import time
import json
import traceback
from PySide6.QtCore import QThread, Signal, Qt, QMetaObject
import utils

from logger import info, warning, error, debug
from utils import get_app_data_dir

class QuickFunctionWorker(QThread):
    """快捷一键功能工作线程类"""
    
    # 信号定义
    progress_updated = Signal(int, int)  # 总进度的当前进度和总值
    function_progress_updated = Signal(int, int)  # 当前功能的进度
    function_started = Signal(str)  # 开始执行功能
    function_completed = Signal(str, bool)  # 功能执行完成，成功/失败
    log_produced = Signal(str, str)  # 日志内容和类型
    all_completed = Signal(bool)  # 全部完成，成功/失败
    request_start_cursor = Signal()  # 请求启动Cursor的信号
    
    def __init__(self, parent=None, config=None):
        """初始化快捷一键功能工作线程
        
        Args:
            parent: 父对象
            config: 配置信息，包含需要执行的功能和延迟时间
        """
        super().__init__(parent)
        self.config = config or {}
        self.stop_flag = False
        self.registered_email = None  # 存储成功注册的邮箱
        
    def run(self):
        """线程主函数，按顺序执行所有开启的功能"""
        try:
            # 获取主窗口用于断开信号
            dialog = self.parent()
            main_window = dialog.parent().window() if dialog else None
            
            # 记录所有需要恢复的信号连接
            original_signal_connections = {}
            
            try:
                # 临时断开可能触发UI更新的信号
                # 保存原有的toast_request信号连接
                if hasattr(main_window, 'toast_request') and hasattr(main_window, 'show_toast'):
                    try:
                        # 保存连接状态并断开
                        toast_connections = main_window.toast_request.disconnect()
                        original_signal_connections['toast_request'] = toast_connections
                        self.log_produced.emit("已临时断开UI更新信号", "info")
                    except Exception as e:
                        self.log_produced.emit(f"断开信号时出现非致命错误: {str(e)}", "warning")
            except Exception as e:
                self.log_produced.emit(f"初始化信号状态时出错: {str(e)}", "warning")
            
            # 获取配置的功能列表
            functions = self.config.get("functions", {})
            delay = self.config.get("delay", 2)
            
            # 定义功能和对应的执行方法
            function_methods = {
                "save_current_account": self._save_current_account,
                "reset_machine_id": self._reset_machine_id,
                "backup_cursor_settings": self._backup_cursor_settings,
                "backup_cursor_workspace": self._backup_cursor_workspace,
                "initialize_cursor": self._initialize_cursor,
                "auto_register": self._auto_register,
                "restore_cursor_settings": self._restore_cursor_settings,
                "restore_cursor_workspace": self._restore_cursor_workspace
            }
            
            # 准备执行的功能列表
            enabled_functions = [f for f, enabled in functions.items() if enabled]
            
            if not enabled_functions:
                self.log_produced.emit("没有开启任何功能，无需执行", "warning")
                self.all_completed.emit(False)
                return
                
            total_functions = len(enabled_functions)
            self.log_produced.emit(f"共有 {total_functions} 个功能需要执行", "info")
            
            # 执行每个功能
            for i, func_id in enumerate(enabled_functions):
                if self.stop_flag:
                    self.log_produced.emit("用户取消执行", "warning")
                    self.all_completed.emit(False)
                    return
                
                # 更新总进度
                progress = int((i / total_functions) * 100)
                self.progress_updated.emit(progress, 100)
                
                # 获取功能执行方法
                method = function_methods.get(func_id)
                
                if not method:
                    self.log_produced.emit(f"未找到功能 {func_id} 的执行方法，跳过", "error")
                    continue
                
                # 开始执行功能
                function_name = self._get_function_name(func_id)
                self.function_started.emit(function_name)
                self.log_produced.emit(f"开始执行: {function_name}", "info")
                
                # 特殊处理初始化Cursor功能
                if func_id == "initialize_cursor":
                    try:
                        # 初始化Cursor可能导致GUI崩溃，使用额外的异常处理
                        self.log_produced.emit("启动初始化Cursor（带额外保护）...", "info")
                        success = method()
                    except Exception as e:
                        self.log_produced.emit(f"初始化Cursor过程中出现意外错误: {str(e)}", "error")
                        success = False
                else:
                    # 执行其他功能
                    success = method()
                
                # 功能执行完成
                self.function_completed.emit(function_name, success)
                
                # 检查是否在失败时停止
                if not success and self.config.get("stop_on_failure", True):
                    self.log_produced.emit(f"{function_name} 执行失败，根据设置停止后续执行", "warning")
                    self.stop_flag = True
                    self.all_completed.emit(False)
                    return
                
                # 添加延迟，除非是最后一个功能或用户取消
                if i < total_functions - 1 and not self.stop_flag and delay > 0:
                    self.log_produced.emit(f"等待 {delay} 秒后执行下一个功能...", "info")
                    for j in range(delay):
                        if self.stop_flag:
                            break
                        time.sleep(1)
            
            # 完成所有功能
            self.progress_updated.emit(100, 100)
            self.log_produced.emit("所有功能执行完成", "success")
            self.all_completed.emit(True)
            
        except Exception as e:
            error_msg = f"执行过程中出现异常: {str(e)}\n{traceback.format_exc()}"
            self.log_produced.emit(error_msg, "error")
            self.all_completed.emit(False)
        finally:
            # 恢复信号连接
            try:
                if main_window and hasattr(main_window, 'toast_request') and hasattr(main_window, 'show_toast'):
                    # 尝试恢复toast_request信号
                    if original_signal_connections.get('toast_request', False):
                        try:
                            main_window.toast_request.connect(main_window.show_toast)
                            self.log_produced.emit("已恢复UI更新信号连接", "info")
                        except Exception as e:
                            self.log_produced.emit(f"恢复信号连接时出错: {str(e)}", "warning")
            except Exception as e:
                # 最后的安全网，确保不会因为信号恢复失败而中断线程清理
                print(f"在finally块中恢复信号时出现异常: {str(e)}")
            
            # 清理任何可能的资源
            self.log_produced.emit("工作线程完成，清理资源...", "info")
    
    def stop(self):
        """停止执行"""
        self.stop_flag = True
        self.log_produced.emit("正在停止执行...", "warning")
    
    def _get_function_name(self, func_id):
        """获取功能的显示名称"""
        names = {
            "save_current_account": "保存当前登录账户",
            "reset_machine_id": "重置机器码",
            "backup_cursor_settings": "备份Cursor设置",
            "backup_cursor_workspace": "备份Cursor会话记录",
            "initialize_cursor": "初始化Cursor",
            "auto_register": "自动注册Cursor账号",
            "restore_cursor_settings": "恢复Cursor设置",
            "restore_cursor_workspace": "恢复Cursor会话记录"
        }
        return names.get(func_id, func_id)
    
    def _save_current_account(self):
        """执行保存当前登录账户功能（避免任何UI交互）"""
        try:
            # 获取主窗口
            dialog = self.parent()
            main_window = dialog.parent().window()
            
            # 获取当前账户信息
            current_email = main_window.current_email
            if not current_email:
                # 尝试从数据库获取邮箱
                if hasattr(main_window, 'auth_manager'):
                    db_email = main_window.auth_manager.get_current_email()
                    if db_email:
                        current_email = db_email
                        self.log_produced.emit(f"使用数据库中的邮箱: {current_email}", "info")
                    else:
                        self.log_produced.emit("没有当前登录邮箱，无法保存", "error")
                        return False
                else:
                    self.log_produced.emit("没有当前登录邮箱，无法保存", "error")
                    return False
            
            self.log_produced.emit(f"准备保存账户: {current_email}", "info")
            
            # 获取系统类型
            import sys
            platform = sys.platform
            system_type = "windows" if platform == "win32" else "mac" if platform == "darwin" else "linux"
            
            # 获取当前账户数据
            if hasattr(main_window, 'account_data'):
                account_data = main_window.account_data
            else:
                self.log_produced.emit("缺少账户数据管理对象，无法保存", "error")
                return False
            
            # 检查账户是否已存在（注意：考虑大小写不敏感）
            account_exists = False
            for account in account_data.accounts:
                if account.get('email', '').lower() == current_email.lower():
                    account_exists = True
                    break
                
            if account_exists:
                self.log_produced.emit(f"发现账户已存在(忽略大小写): {current_email}，询问是否更新", "info")
                
                # 静默确认更新
                self.log_produced.emit(f"用户确认更新账户: {current_email}", "info")
            
            # 获取令牌信息
            self.log_produced.emit("开始从SQLite数据库获取token信息", "info")
            
            import sqlite3
            import os
            
            # 获取数据库路径
            if sys.platform == "win32":  # Windows
                appdata = os.getenv("APPDATA")
                if appdata is None:
                    self.log_produced.emit("APPDATA 环境变量未设置", "error")
                    return False
                db_path = os.path.join(appdata, "Cursor", "User", "globalStorage", "state.vscdb")
            elif sys.platform == "darwin": # macOS
                db_path = os.path.abspath(os.path.expanduser(
                    "~/Library/Application Support/Cursor/User/globalStorage/state.vscdb"
                ))
            elif sys.platform == "linux": # Linux 和其他类Unix系统
                db_path = os.path.abspath(os.path.expanduser(
                    "~/.config/Cursor/User/globalStorage/state.vscdb"
                ))
            else:
                self.log_produced.emit(f"不支持的操作系统: {sys.platform}", "error")
                return False
                
            # 检查数据库是否存在
            if not os.path.exists(db_path):
                self.log_produced.emit(f"数据库文件不存在: {db_path}", "error")
                return False
                
            # 连接数据库
            conn = None
            access_token = None
            refresh_token = None
            
            try:
                conn = sqlite3.connect(db_path)
                self.log_produced.emit(f"成功连接到SQLite数据库: {db_path}", "info")
                cursor = conn.cursor()
                
                # 查询accessToken
                cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/accessToken'")
                result = cursor.fetchone()
                if result:
                    access_token = result[0]
                    self.log_produced.emit(f"成功从数据库获取accessToken，长度: {len(access_token)}", "info")
                else:
                    self.log_produced.emit("数据库中没有找到accessToken", "error")
                    if conn: conn.close()
                    return False
                    
                # 查询refreshToken
                cursor.execute("SELECT value FROM itemTable WHERE key = 'cursorAuth/refreshToken'")
                result = cursor.fetchone()
                if result:
                    refresh_token = result[0]
                    self.log_produced.emit(f"成功从数据库获取refreshToken，长度: {len(refresh_token)}", "info")
                else:
                    self.log_produced.emit("数据库中没有找到refreshToken", "warning")
                    # 使用accessToken作为refreshToken的替代
                    refresh_token = access_token
            except Exception as e:
                self.log_produced.emit(f"访问数据库时出错: {str(e)}", "error")
                if conn: conn.close()
                return False
            finally:
                if conn:
                    conn.close()
                    self.log_produced.emit("关闭SQLite数据库连接", "info")
            
            # 获取telemetry信息（包含机器码）
            self.log_produced.emit("开始获取telemetry和系统机器码信息", "info")
            
            import json
            
            # 获取storage.json文件
            if sys.platform == "win32":  # Windows
                storage_path = os.path.join(appdata, "Cursor", "User", "globalStorage", "storage.json")
            elif sys.platform == "darwin": # macOS
                storage_path = os.path.abspath(os.path.expanduser(
                    "~/Library/Application Support/Cursor/User/globalStorage/storage.json"
                ))
            elif sys.platform == "linux": # Linux
                storage_path = os.path.abspath(os.path.expanduser(
                    "~/.config/Cursor/User/globalStorage/storage.json"
                ))
            
            self.log_produced.emit(f"storage.json路径: {storage_path}", "info")
            
            # 读取storage.json文件
            telemetry_data = {}
            machine_id = ""
            mac_machine_id = ""
            dev_device_id = ""
            sqm_id = ""
            
            if os.path.exists(storage_path):
                self.log_produced.emit("找到storage.json文件，准备读取", "info")
                try:
                    with open(storage_path, 'r', encoding='utf-8') as f:
                        storage_data = json.load(f)
                        self.log_produced.emit("成功读取storage.json文件内容", "info")
                        
                        # 获取telemetry数据
                        telemetry_data = storage_data.get('telemetry', {})
                        machine_id = telemetry_data.get('machineId', '')
                        mac_machine_id = telemetry_data.get('macMachineId', '')
                        dev_device_id = telemetry_data.get('devDeviceId', '')
                        sqm_id = telemetry_data.get('sqmId', '')
                        
                        # 输出信息
                        if machine_id:
                            # 只显示前几个字符，避免泄露完整机器码
                            self.log_produced.emit(f"获取到telemetry.machineId: {machine_id[:10]}...", "info")
                        if mac_machine_id:
                            self.log_produced.emit(f"获取到telemetry.macMachineId: {mac_machine_id[:10]}...", "info")
                        if dev_device_id:
                            self.log_produced.emit(f"获取到telemetry.devDeviceId: {dev_device_id[:10]}...", "info")
                        if sqm_id:
                            self.log_produced.emit(f"获取到telemetry.sqmId: {sqm_id[:10]}...", "info")
                except Exception as e:
                    self.log_produced.emit(f"读取storage.json时出错: {str(e)}", "error")
            
            # 获取Windows MachineGuid
            windows_machine_guid = ""
            if system_type == "windows":
                self.log_produced.emit("开始获取windows系统特定的机器码", "info")
                try:
                    import winreg
                    self.log_produced.emit("尝试获取Windows MachineGuid", "info")
                    
                    # 打开注册表键
                    reg_key = winreg.OpenKey(
                        winreg.HKEY_LOCAL_MACHINE,
                        r"SOFTWARE\Microsoft\Cryptography"
                    )
                    
                    # 读取MachineGuid值
                    windows_machine_guid, _ = winreg.QueryValueEx(reg_key, "MachineGuid")
                    self.log_produced.emit(f"通过winreg API获取到Windows MachineGuid: {windows_machine_guid}", "info")
                    
                    # 关闭注册表键
                    winreg.CloseKey(reg_key)
                except Exception as e:
                    self.log_produced.emit(f"获取Windows MachineGuid失败: {str(e)}", "error")
            
            # 准备保存账户
            auth_info = {
                'cursorAuth/accessToken': access_token,
                'cursorAuth/refreshToken': refresh_token
            }
            
            # 机器码信息
            machine_info = {
                'system_type': system_type,
                'telemetry': telemetry_data
            }
            
            # 添加特定系统的机器码
            if system_type == "windows" and windows_machine_guid:
                machine_info['windows_machine_guid'] = windows_machine_guid
            
            # 更新账户数据
            if account_exists:
                # 更新账户
                for account in account_data.accounts:
                    if account.get('email', '').lower() == current_email.lower():
                        account['email'] = current_email  # 使用精确大小写
                        account['auth_info'] = auth_info
                        account['machine_info'] = machine_info
                        self.log_produced.emit("已更新账户的机器码信息", "info")
                        self.log_produced.emit(f"更新账户在列表中的数据: {current_email}", "info")
                        break
            else:
                # 添加新账户
                new_account = {
                    'email': current_email,
                    'auth_info': auth_info,
                    'machine_info': machine_info
                }
                account_data.accounts.append(new_account)
                self.log_produced.emit(f"添加新账户到列表: {current_email}", "info")
            
            # 保存到文件
            self.log_produced.emit("开始保存账户列表到文件", "info")
            if account_data.save_accounts():
                self.log_produced.emit(f"账户 {current_email} 已成功{('更新' if account_exists else '保存')}", "info")
                return True
            else:
                self.log_produced.emit(f"保存账户 {current_email} 到文件失败", "error")
                return False
                
        except Exception as e:
            self.log_produced.emit(f"保存当前登录账户时发生错误: {str(e)}", "error")
            return False

    def _reset_machine_id(self):
        """执行重置机器码功能"""
        try:
            self.log_produced.emit("开始执行重置机器码", "info")
            self.log_produced.emit(f"系统类型: {sys.platform}", "info")
            
            # 创建重置机器码工作线程，但不显示对话框
            from widgets.reset_machine_id_dialog import ResetMachineIDWorker
            worker = ResetMachineIDWorker()
            
            # 日志记录worker创建成功
            self.log_produced.emit("重置机器码工作线程创建成功", "info")
            
            # 保存原始确认方法，用于可能的对话框询问
            import utils
            original_confirm = utils.Utils.confirm_message
            
            # 替换为始终返回True的方法
            def always_confirm(*args, **kwargs):
                self.log_produced.emit("静默确认对话框", "info")
                return True
            
            # 设置临时方法
            utils.Utils.confirm_message = always_confirm
            
            try:
                # 连接信号到日志
                worker.log_produced.connect(
                    lambda text, log_type: self.log_produced.emit(text, log_type)
                )
                worker.progress_updated.connect(self.function_progress_updated)
                
                # 日志记录开始执行
                self.log_produced.emit("开始执行重置机器码线程", "info")
                
                # 执行并等待完成
                worker.start()
                
                # 等待工作线程完成，同时提供状态更新
                import time
                wait_count = 0
                while worker.isRunning():
                    time.sleep(0.5)
                    wait_count += 1
                    if wait_count % 10 == 0:  # 每5秒报告一次等待状态
                        self.log_produced.emit(f"等待重置机器码完成...已等待{wait_count/2}秒", "info")
                    
                    # 检查是否需要停止等待
                    if self.stop_flag:
                        self.log_produced.emit("用户取消执行，正在停止重置机器码", "warning")
                        worker.terminate()
                        return False
                
                # 检查最终结果
                if not worker.isRunning():
                    self.log_produced.emit("重置机器码线程已完成", "info")
                    self.log_produced.emit("重置机器码执行完成", "info")
                    return True
                else:
                    self.log_produced.emit("重置机器码线程未正常完成", "error")
                    return False
            finally:
                # 恢复原始方法
                utils.Utils.confirm_message = original_confirm
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            self.log_produced.emit(f"重置机器码失败: {str(e)}", "error")
            self.log_produced.emit(f"错误详情: {error_details}", "error")
            return False

    def _backup_cursor_settings(self):
        """执行备份Cursor设置功能"""
        try:
            # 获取主窗口
            dialog = self.parent()
            main_window = dialog.parent().window()
            
            # 从主窗口获取功能页面实例
            functionality_page = main_window.functionality_page
            
            # 保存原始确认方法，用于可能的对话框询问
            import utils
            original_confirm = utils.Utils.confirm_message
            
            # 替换为始终返回True的方法
            def always_confirm(*args, **kwargs):
                return True
            
            # 设置临时方法
            utils.Utils.confirm_message = always_confirm
            
            try:
                # 直接调用备份方法，避免UI交互
                success = functionality_page._backup_cursor_settings()
                
                # 无论结果如何，只要没有抛出异常都视为成功
                # 因为通常备份时即使部分文件失败，整体也算成功
                if not success:
                    self.log_produced.emit("部分设置备份未完成，但主要备份已执行", "warning")
                return True
            finally:
                # 恢复原始方法
                utils.Utils.confirm_message = original_confirm
        except Exception as e:
            self.log_produced.emit(f"备份Cursor设置失败: {str(e)}", "error")
            return False

    def _backup_cursor_workspace(self):
        """执行备份Cursor会话记录功能"""
        try:
            # 获取主窗口和各种路径
            dialog = self.parent()
            main_window = dialog.parent().window()
            functionality_page = main_window.functionality_page
            
            # 使用functionality_page中的方法获取路径，但操作在本线程中完成
            self.log_produced.emit("开始备份Cursor会话记录...", "info")
            
            # 导入需要的模块
            from utils import get_app_data_dir
            import os
            import zipfile
            import shutil
            import json
            import sqlite3
            
            # 创建备份目录
            backup_dir = os.path.join(get_app_data_dir(), "backups", "workspaceStorage")
            os.makedirs(backup_dir, exist_ok=True)
            
            # 检查备份目录是否创建成功
            if not os.path.exists(backup_dir):
                self.log_produced.emit("无法创建备份目录", "error")
                return False
            
            # 获取workspaceStorage路径
            workspace_path = functionality_page._get_cursor_workspace_storage_path()
            if not workspace_path or not os.path.exists(workspace_path):
                self.log_produced.emit("Cursor会话记录文件夹不存在", "error")
                return False
            
            # 获取History路径
            history_path = functionality_page._get_cursor_history_path()
            history_exists = history_path and os.path.exists(history_path)
            if not history_exists:
                self.log_produced.emit("Cursor History文件夹不存在，将只备份workspaceStorage", "info")
            
            # 获取globalStorage路径
            global_storage_path = functionality_page._get_cursor_global_storage_path()
            state_vscdb_path = os.path.join(global_storage_path, "state.vscdb") if global_storage_path else None
            state_vscdb_exists = state_vscdb_path and os.path.exists(state_vscdb_path)
            
            # 开始备份workspaceStorage
            self.log_produced.emit("正在备份Cursor会话记录...", "info")
            workspace_backup_file = os.path.join(backup_dir, "workspaceStorage.zip")
            with zipfile.ZipFile(workspace_backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # 将workspaceStorage文件夹内容添加到压缩文件
                for root, dirs, files in os.walk(workspace_path):
                    for file in files:
                        file_path = os.path.join(root, file)
                        arcname = os.path.relpath(file_path, os.path.dirname(workspace_path))
                        zipf.write(file_path, arcname)
            
            # 如果History存在，也进行备份
            if history_exists:
                self.log_produced.emit("正在备份Cursor History...", "info")
                history_backup_file = os.path.join(backup_dir, "History.zip")
                with zipfile.ZipFile(history_backup_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    # 将History文件夹内容添加到压缩文件
                    for root, dirs, files in os.walk(history_path):
                        for file in files:
                            file_path = os.path.join(root, file)
                            arcname = os.path.relpath(file_path, os.path.dirname(history_path))
                            zipf.write(file_path, arcname)
            
            # 如果state.vscdb存在，备份cursorDiskKV表
            if state_vscdb_exists:
                self.log_produced.emit("正在备份cursorDiskKV表...", "info")
                cursor_disk_kv_backup_file = os.path.join(backup_dir, "cursorDiskKV.json")
                # 连接数据库
                conn = sqlite3.connect(state_vscdb_path)
                cursor = conn.cursor()
                
                try:
                    # 检查表是否存在
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cursorDiskKV'")
                    if cursor.fetchone() is not None:
                        # 查询表结构
                        cursor.execute("PRAGMA table_info('cursorDiskKV')")
                        columns = [row[1] for row in cursor.fetchall()]
                        
                        # 查询表中所有数据
                        cursor.execute("SELECT * FROM cursorDiskKV")
                        rows = cursor.fetchall()
                        
                        # 构造数据结构
                        data = []
                        for row in rows:
                            row_data = {}
                            for i, col in enumerate(columns):
                                if i < len(row):
                                    row_data[col] = row[i]
                            data.append(row_data)
                        
                        # 构造备份数据结构
                        backup_data = {
                            "table_name": "cursorDiskKV",
                            "columns": columns,
                            "data": data
                        }
                        
                        # 保存为JSON
                        with open(cursor_disk_kv_backup_file, 'w', encoding='utf-8') as f:
                            json.dump(backup_data, f, ensure_ascii=False, indent=2)
                            self.log_produced.emit("已备份cursorDiskKV表数据", "info")
                    else:
                        self.log_produced.emit("cursorDiskKV表不存在，跳过备份", "warning")
                except Exception as e:
                    self.log_produced.emit(f"备份cursorDiskKV表失败: {str(e)}", "error")
                finally:
                    if 'cursor' in locals() and cursor:
                        cursor.close()
                    if 'conn' in locals() and conn:
                        conn.close()
            else:
                self.log_produced.emit("Cursor state.vscdb文件不存在，跳过备份cursorDiskKV表", "warning")
            
            self.log_produced.emit(f"已备份Cursor会话记录", "info")
            if history_exists:
                self.log_produced.emit("已备份Cursor History", "info")
            
            return True
                    
        except Exception as e:
            self.log_produced.emit(f"备份Cursor会话记录和History失败: {str(e)}", "error")
            return False

    def _initialize_cursor(self):
        """执行初始化Cursor功能"""
        try:
            # 获取主窗口
            dialog = self.parent()
            main_window = dialog.parent().window()
            
            # 从主窗口获取功能页面实例
            functionality_page = main_window.functionality_page
            
            # 保存原始确认方法，用于可能的对话框询问
            import utils
            original_confirm = utils.Utils.confirm_message
            
            # 替换为始终返回True的方法
            def always_confirm(*args, **kwargs):
                return True
            
            # 设置临时方法
            utils.Utils.confirm_message = always_confirm
            
            try:
                # 直接调用初始化方法，不通过UI事件
                # 设置auto_restart为True，避免显示对话框
                result, error_msg = functionality_page._initialize_cursor(auto_restart=True)
                
                if result in ["success_restart"]:
                    self.log_produced.emit("初始化成功，准备启动Cursor", "info")
                    
                    # 重要改进：不再在工作线程中使用QTimer
                    # 而是发射信号，让主线程处理启动Cursor的操作
                    import time
                    time.sleep(1)  # 先等待1秒确保文件操作完成
                    
                    self.log_produced.emit("发出启动Cursor请求信号...", "info")
                    # 发射请求启动Cursor的信号
                    self.request_start_cursor.emit()
                    return True
                elif result in ["success", "success_no_restart"]:
                    self.log_produced.emit("初始化成功，但不需要重启Cursor", "info")
                    return True
                else:
                    self.log_produced.emit(f"初始化失败: {result}, {error_msg}", "error")
                    return False
            except Exception as e:
                self.log_produced.emit(f"初始化过程中出现异常: {str(e)}", "error")
                return False
            finally:
                # 确保恢复原始方法
                utils.Utils.confirm_message = original_confirm
                
        except Exception as e:
            self.log_produced.emit(f"初始化Cursor时发生错误: {str(e)}", "error")
            return False

    def _auto_register(self):
        """执行自动注册Cursor账号功能"""
        try:
            
            # 获取主窗口
            dialog = self.parent()
            main_window = dialog.parent().window()
            
            # 从utils导入所需的功能
            import os
            import json
            import sys
            import io
            from utils import get_app_data_dir
            from account.account_data import AccountData
            
            # 创建全局日志捕获对象
            class LogCapture:
                def __init__(self, emit_function):
                    self.emit = emit_function
                    self.stdout = sys.stdout
                    self.buffer = io.StringIO()
                
                def write(self, message):
                    # 写入缓冲区
                    self.buffer.write(message)
                    # 同时写入原始stdout
                    self.stdout.write(message)
                    
                    # 处理完整行
                    if '\n' in message:
                        self.flush()
                
                def flush(self):
                    # 获取缓冲区内容
                    content = self.buffer.getvalue()
                    if content.strip():
                        # 将日志输出到UI - 在自动注册过程中禁用
                        # self.emit(content.strip(), "info")
                        pass
                    # 清空缓冲区
                    self.buffer = io.StringIO()
                    # 刷新原始stdout
                    self.stdout.flush()
                
                def start(self):
                    sys.stdout = self
                
                def stop(self):
                    sys.stdout = self.stdout
                    self.flush()  # 确保处理最后的内容
            
            # 创建日志捕获对象实例
            log_capture = LogCapture(self.log_produced.emit)
            
            # 获取配置文件路径
            settings_file = os.path.join(get_app_data_dir(), "settings.json")
            function_config_file = os.path.join(get_app_data_dir(), "config", "function.json")
            
            # 获取账号文件路径，用于检查注册是否真的成功
            account_data = AccountData()
            accounts_file = account_data.accounts_file
            
            # 记录账号文件现有状态
            existing_accounts = []
            existing_emails = set()
            if os.path.exists(accounts_file):
                try:
                    with open(accounts_file, 'r', encoding='utf-8') as f:
                        existing_accounts = json.load(f)
                        existing_emails = {acc.get('email', '').lower() for acc in existing_accounts}
                except Exception as e:
                    self.log_produced.emit(f"读取账号文件出错: {str(e)}", "warning")
            
            # 记录账号文件的修改时间
            account_file_mtime_before = os.path.getmtime(accounts_file) if os.path.exists(accounts_file) else 0
            
            # 加载settings.json配置
            settings = {}
            if os.path.exists(settings_file):
                try:
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                except Exception as e:
                    self.log_produced.emit(f"读取settings.json出错: {str(e)}", "error")
                    return False
            else:
                self.log_produced.emit("未找到settings.json文件", "error")
                return False
                
            # 加载功能配置文件
            function_settings = {}
            if os.path.exists(function_config_file):
                try:
                    with open(function_config_file, 'r', encoding='utf-8') as f:
                        function_settings = json.load(f)
                except Exception as e:
                    self.log_produced.emit(f"读取功能配置文件出错: {str(e)}", "error")
                    # 使用默认配置
                    function_settings = {"register_only": False, "register_method": 1}
            else:
                function_settings = {"register_only": False, "register_method": 1}
                
            # 验证配置是否完整
            # 检查关键配置项
            required_configs = [
                "auto_register_domain",
                "auto_register_email_type"
            ]
            
            for config in required_configs:
                if config not in settings:
                    self.log_produced.emit(f"配置缺失: {config}", "error")
                    return False
                    
            # 根据邮箱类型检查特定配置项
            email_type = settings.get("auto_register_email_type", "")
            
            if email_type == "temp":
                # 检查临时邮箱配置
                temp_mail = settings.get("auto_register_temp_mail", "")
                temp_mail_epin = settings.get("auto_register_temp_mail_epin", "")
                
                if not temp_mail or not temp_mail_epin:
                    self.log_produced.emit("临时邮箱配置不完整", "error")
                    return False
                else:
                    pass
            elif email_type == "imap":
                # 检查IMAP邮箱配置
                imap_server = settings.get("auto_register_imap_server", "")
                imap_port = settings.get("auto_register_imap_port", "")
                imap_user = settings.get("auto_register_imap_user", "")
                imap_pass = settings.get("auto_register_imap_pass", "")
                
                if not imap_server or not imap_port or not imap_user or not imap_pass:
                    self.log_produced.emit("IMAP邮箱配置不完整", "error")
                    return False
                else:
                    pass
            else:
                self.log_produced.emit(f"未知的邮箱类型: {email_type}", "error")
                return False
            
            # 检查域名配置
            domain = settings.get("auto_register_domain", "")
            if not domain:
                self.log_produced.emit("域名配置为空", "error")
                return False
                
            domains = [d.strip() for d in domain.split(",") if d.strip()]
            if not domains:
                self.log_produced.emit("域名配置无效，请检查格式", "error")
                return False

            
            # 直接导入并创建AutoRegisterWorker实例
            from widgets.auto_register_dialog import AutoRegisterWorker
            

            worker = AutoRegisterWorker(settings)
            
            # 配置工作线程
            register_only = function_settings.get("register_only", False)
            register_method = function_settings.get("register_method", 1)
            
            worker.register_only = register_only
            worker.register_method = register_method
            
            method_info = "方案一" if register_method == 1 else "方案二"
                
            # 创建结果跟踪变量
            success_result = [False]
            operation_completed = [False]
            
            # 定义完成回调函数
            def on_operation_finished(success, message):
                success_result[0] = success
                operation_completed[0] = True
                
                # 如果成功并且不是仅注册模式，保存账号
                if success and not register_only and hasattr(main_window, 'save_current_account'):
                    try:
                        main_window.save_current_account()
                        main_window.load_current_account()
                    except Exception:
                        pass
            
            # 连接信号 - 直接透传所有日志
            worker.progress_updated.connect(self.function_progress_updated)
            worker.operation_finished.connect(on_operation_finished)
            
            # 开始执行
            
            # 启动日志捕获
            log_capture.start()
            
            # 启动工作线程
            worker.start()
            
            # 等待工作线程完成，同时提供状态更新
            import time
            wait_count = 0
            max_wait_time = 900  # 最长等待15分钟
            
            # 每30秒检查一次账号文件
            last_check_time = time.time()
            
            while worker.isRunning() and wait_count < max_wait_time:
                time.sleep(1)
                wait_count += 1
                
                # 每30秒检查一次账号文件变化
                if wait_count % 30 == 0:
                    
                    # 检查账号文件是否变化 (无论时间多长都检查)
                    try:
                        if os.path.exists(accounts_file):
                            current_mtime = os.path.getmtime(accounts_file)
                            if current_mtime > account_file_mtime_before:
                                with open(accounts_file, 'r', encoding='utf-8') as f:
                                    current_accounts = json.load(f)
                                    current_emails = {acc.get('email', '').lower() for acc in current_accounts}
                                    new_emails = current_emails - existing_emails
                                    if new_emails:
                                        pass
                    except Exception:
                        pass
            
                # 检查是否需要停止等待
                if self.stop_flag:
                    worker.stop()
                    # 停止日志捕获
                    log_capture.stop()
                    return False
            
            # 如果超时但未完成
            if worker.isRunning() and not operation_completed[0]:
                
                # 首先检查账号文件变化，这是最直接的成功证据
                success_by_file_change = False
                try:
                    if os.path.exists(accounts_file):
                        account_file_mtime_after = os.path.getmtime(accounts_file)
                        if account_file_mtime_after > account_file_mtime_before:
                            with open(accounts_file, 'r', encoding='utf-8') as f:
                                current_accounts = json.load(f)
                                current_emails = {acc.get('email', '').lower() for acc in current_accounts}
                                
                                # 检查是否有新邮箱
                                new_emails = current_emails - existing_emails
                                if new_emails:
                                    # 保存注册的邮箱信息
                                    self.registered_email = list(new_emails)[0] if new_emails else None
                                    success_by_file_change = True
                except Exception:
                    pass
                
                # 无论成功与否，都需要停止工作线程
                worker.stop()
                if not worker.wait(3000):  # 等待3秒
                    worker.terminate()
                
                # 停止日志捕获
                log_capture.stop()
                
                # 如果通过文件变化确认已成功，则返回成功
                if success_by_file_change:
                    return True
                
                # 否则返回失败
                return False
            
            # 停止日志捕获
            log_capture.stop()
            
            # 如果工作线程已经完成或被停止，但我们仍然需要确认最终结果
            
            # 先检查工作线程的信号回调是否设置了成功状态
            if operation_completed[0] and success_result[0]:
                return True
                
            # 如果信号回调没有报告成功，检查账号文件变化
            try:
                if os.path.exists(accounts_file):
                    final_mtime = os.path.getmtime(accounts_file)
                    if final_mtime > account_file_mtime_before:
                        with open(accounts_file, 'r', encoding='utf-8') as f:
                            final_accounts = json.load(f)
                            final_emails = {acc.get('email', '').lower() for acc in final_accounts}
                            
                            # 检查是否有新邮箱
                            final_new_emails = final_emails - existing_emails
                            if final_new_emails:
                                # 保存注册的邮箱信息
                                self.registered_email = list(final_new_emails)[0] if final_new_emails else None
                                return True
            except Exception:
                pass
            
            # 如果没有检测到账号文件变化，使用工作线程报告的结果
            return success_result[0]
            
        except Exception as e:
            return False

    def _restore_cursor_settings(self):
        """执行恢复Cursor设置功能"""
        try:
            # 获取主窗口
            dialog = self.parent()
            main_window = dialog.parent().window()
            
            # 获取功能页面
            functionality_page = main_window.functionality_page
            
            # 保存原始确认方法，用于可能的对话框询问
            import utils
            original_confirm = utils.Utils.confirm_message
            
            # 替换为始终返回True的方法
            def always_confirm(*args, **kwargs):
                return True
            
            # 设置临时方法
            utils.Utils.confirm_message = always_confirm
            
            try:
                # 直接调用恢复设置方法，避免UI交互
                success = functionality_page._restore_cursor_settings()
                
                # 无论结果如何，只要没有抛出异常都视为成功
                # 因为通常即使部分文件恢复失败，整体也算成功
                if not success:
                    self.log_produced.emit("部分设置恢复可能未完成，但过程已执行", "warning")
                return True
            finally:
                # 恢复原始方法
                utils.Utils.confirm_message = original_confirm
        except Exception as e:
            self.log_produced.emit(f"恢复Cursor设置失败: {str(e)}", "error")
            return False

    def _restore_cursor_workspace(self):
        """执行恢复Cursor会话记录功能"""
        try:
            # 获取主窗口和各种路径
            dialog = self.parent()
            main_window = dialog.parent().window()
            functionality_page = main_window.functionality_page

            # 使用functionality_page中的方法获取路径，但操作在本线程中完成
            self.log_produced.emit("开始恢复Cursor会话记录...", "info")
            
            # 先终止Cursor进程
            self.log_produced.emit("正在关闭Cursor进程，准备恢复备份", "info")
            cursor_killed = False
            
            try:
                # 调用主窗口的kill_cursor_process方法
                cursor_killed = main_window.kill_cursor_process()
                if cursor_killed:
                    self.log_produced.emit("成功关闭Cursor进程", "info")
                    # 等待2秒确保进程完全终止
                    self.log_produced.emit("等待2秒确保进程完全终止...", "info")
                    time.sleep(2)
                else:
                    self.log_produced.emit("未检测到运行中的Cursor进程，或关闭失败", "info")
            except Exception as e:
                error_msg = str(e)
                self.log_produced.emit(f"终止Cursor进程失败: {error_msg}", "warning")
                # 继续执行，不影响恢复流程
            
            # 获取各种路径
            from utils import get_app_data_dir
            import os
            import shutil
            import zipfile
            import json
            import sqlite3
            
            # 备份文件路径
            backup_dir = os.path.join(get_app_data_dir(), "backups", "workspaceStorage")
            workspace_backup_file = os.path.join(backup_dir, "workspaceStorage.zip")
            history_backup_file = os.path.join(backup_dir, "History.zip")
            cursor_disk_kv_backup_file = os.path.join(backup_dir, "cursorDiskKV.json")
            
            # 检查workspaceStorage备份是否存在
            if not os.path.exists(workspace_backup_file):
                self.log_produced.emit(f"Cursor会话记录备份文件不存在: {workspace_backup_file}", "error")
                return False
            
            # 检查History备份是否存在
            history_backup_exists = os.path.exists(history_backup_file)
            if not history_backup_exists:
                self.log_produced.emit("Cursor History备份文件不存在，将只恢复workspaceStorage", "warning")
            
            # 检查cursorDiskKV备份是否存在
            cursor_disk_kv_backup_exists = os.path.exists(cursor_disk_kv_backup_file)
            if not cursor_disk_kv_backup_exists:
                self.log_produced.emit("Cursor cursorDiskKV备份文件不存在，将跳过恢复此表", "warning")
            
            # 获取目标目录 - 使用functionality_page的方法
            workspace_path = functionality_page._get_cursor_workspace_storage_path()
            if not workspace_path:
                self.log_produced.emit("无法获取Cursor会话记录路径", "error")
                return False
            
            # 获取History目标路径
            history_path = functionality_page._get_cursor_history_path()
            if history_backup_exists and not history_path:
                self.log_produced.emit("无法获取Cursor History路径", "warning")
                # 不会因为没有History路径而失败
            
            # 获取state.vscdb目标路径
            global_storage_path = functionality_page._get_cursor_global_storage_path()
            state_vscdb_path = os.path.join(global_storage_path, "state.vscdb") if global_storage_path else None
            if cursor_disk_kv_backup_exists and not state_vscdb_path:
                self.log_produced.emit("无法获取Cursor state.vscdb路径", "warning")
                # 不会因为没有state.vscdb路径而失败
            
            try:
                # 恢复workspaceStorage - 完整替换
                # 确保父目录存在
                parent_dir = os.path.dirname(workspace_path)
                os.makedirs(parent_dir, exist_ok=True)
                
                # 如果目标父目录不存在，标记为失败
                if not os.path.exists(parent_dir):
                    self.log_produced.emit("无法创建Cursor会话记录父目录，恢复失败", "error")
                    return False
                
                # 如果目标目录已存在，先删除
                if os.path.exists(workspace_path):
                    shutil.rmtree(workspace_path)
                    self.log_produced.emit("已删除原Cursor会话记录目录，准备恢复", "info")
                
                # 解压备份文件到父目录，实现完整替换
                self.log_produced.emit("正在解压会话记录备份文件...", "info")
                with zipfile.ZipFile(workspace_backup_file, 'r') as zipf:
                    zipf.extractall(parent_dir)
                
                # 确认目录已恢复
                if not os.path.exists(workspace_path):
                    self.log_produced.emit("恢复失败：解压后目标目录不存在", "error")
                    return False
                    
                self.log_produced.emit(f"已从备份完整替换 Cursor会话记录", "info")
                
                # 如果有History备份，也进行完整替换
                if history_backup_exists and history_path:
                    # 确保目标目录的父目录存在
                    history_parent_dir = os.path.dirname(history_path)
                    os.makedirs(history_parent_dir, exist_ok=True)
                    
                    # 如果目标目录已存在，先删除
                    if os.path.exists(history_path):
                        shutil.rmtree(history_path)
                        self.log_produced.emit("已删除原Cursor History目录，准备恢复", "info")
                    
                    # 解压备份文件到父目录，实现完整替换
                    self.log_produced.emit("正在解压History备份文件...", "info")
                    with zipfile.ZipFile(history_backup_file, 'r') as zipf:
                        zipf.extractall(history_parent_dir)
                    
                    # 确认目录已恢复
                    if not os.path.exists(history_path):
                        self.log_produced.emit("警告：History解压后目标目录不存在", "warning")
                    else:
                        self.log_produced.emit("已从备份完整替换 Cursor History", "info")
                
                # 如果有cursorDiskKV备份，也进行恢复
                if cursor_disk_kv_backup_exists and state_vscdb_path and global_storage_path:
                    # 确保目标目录存在
                    os.makedirs(global_storage_path, exist_ok=True)
                    
                    # 如果目标目录创建失败，则跳过cursorDiskKV恢复
                    if not os.path.exists(global_storage_path):
                        self.log_produced.emit("无法创建Cursor globalStorage目录，跳过cursorDiskKV恢复", "warning")
                    else:
                        try:
                            # 读取备份的JSON数据
                            self.log_produced.emit("正在恢复cursorDiskKV表数据...", "info")
                            with open(cursor_disk_kv_backup_file, 'r', encoding='utf-8') as f:
                                backup_data = json.load(f)
                            
                            # 验证备份数据格式
                            if not all(k in backup_data for k in ['table_name', 'columns', 'data']):
                                self.log_produced.emit("cursorDiskKV备份数据格式错误，跳过恢复", "warning")
                            else:
                                # 连接数据库
                                conn = sqlite3.connect(state_vscdb_path)
                                cursor = conn.cursor()
                                
                                try:
                                    # 检查表是否存在，如果不存在则创建表
                                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='cursorDiskKV'")
                                    if cursor.fetchone() is None:
                                        # 创建表
                                        columns = backup_data['columns']
                                        column_defs = ', '.join([f'"{col}" TEXT' for col in columns])
                                        cursor.execute(f'CREATE TABLE "cursorDiskKV" ({column_defs})')
                                        self.log_produced.emit("创建了新的cursorDiskKV表", "info")
                                    else:
                                        # 清空现有表中的所有数据
                                        cursor.execute('DELETE FROM "cursorDiskKV"')
                                        self.log_produced.emit("清空了现有的cursorDiskKV表数据", "info")
                                    
                                    # 恢复数据
                                    for row_data in backup_data['data']:
                                        columns = list(row_data.keys())
                                        placeholders = ', '.join(['?' for _ in columns])
                                        column_names = ', '.join([f'"{col}"' for col in columns])
                                        values = [row_data[col] for col in columns]
                                        
                                        cursor.execute(f'INSERT INTO "cursorDiskKV" ({column_names}) VALUES ({placeholders})', values)
                                    
                                    # 提交事务
                                    conn.commit()
                                    self.log_produced.emit("已从备份恢复Cursor cursorDiskKV表数据", "info")
                                except Exception as e:
                                    self.log_produced.emit(f"恢复cursorDiskKV表数据失败: {str(e)}", "error")
                                    conn.rollback()  # 回滚事务
                                finally:
                                    cursor.close()
                                    conn.close()
                        except Exception as e:
                            self.log_produced.emit(f"读取或处理cursorDiskKV备份数据失败: {str(e)}", "error")
                
                # 获取自动启动设置
                auto_restart_setting = True  # 默认为True
                try:
                    settings_file = os.path.join(get_app_data_dir(), "settings.json")
                    if os.path.exists(settings_file):
                        with open(settings_file, 'r', encoding='utf-8') as f:
                            settings = json.load(f)
                            auto_restart_setting = settings.get("auto_restart_cursor", True)
                except Exception as e:
                    self.log_produced.emit(f"读取设置时出错: {str(e)}", "warning")
                
                # 如果需要重启Cursor
                if cursor_killed and auto_restart_setting:
                    self.log_produced.emit("恢复完成，即将重启Cursor", "info")
                    # 使用QTimer在主线程中触发重启
                    try:
                        from PySide6.QtCore import QTimer, QMetaObject, Qt
                        QMetaObject.invokeMethod(
                            main_window, 
                            "start_cursor_app", 
                            Qt.ConnectionType.QueuedConnection
                        )
                    except Exception as e:
                        self.log_produced.emit(f"重启Cursor失败: {str(e)}", "error")
                        # 即使重启失败，恢复操作本身也是成功的
                
                self.log_produced.emit("Cursor会话记录恢复成功", "info")
                return True
                
            except Exception as e:
                error_msg = str(e)
                self.log_produced.emit(f"恢复Cursor会话记录失败: {error_msg}", "error")
                return False
                
        except Exception as e:
            self.log_produced.emit(f"恢复Cursor会话记录失败: {str(e)}", "error")
            return False

    # 增加一个辅助函数来分析过程中记录的日志，查找成功证据
    def analyze_logs_for_success_evidence(self):
        try:
            # 尝试获取自动注册生成的日志文件
            app_data_dir = get_app_data_dir()
            log_dir = os.path.join(app_data_dir, "logs")
            
            # 检查最常见的成功证据文件
            register_success_log = os.path.join(log_dir, "auto_register_success.log")
            if os.path.exists(register_success_log):
                return True
            
            # 其次，检查可能的一般日志文件
            auto_register_log = os.path.join(log_dir, "auto_register.log")
            if os.path.exists(auto_register_log):
                # 读取日志内容查找成功关键词
                try:
                    with open(auto_register_log, 'r', encoding='utf-8') as f:
                        log_content = f.read()
                        success_keywords = [
                            "注册成功", "成功创建账号", "验证码登录成功", 
                            "账号数据保存成功", "成功获取token", "accessToken获取成功"
                        ]
                        if any(keyword in log_content for keyword in success_keywords):
                            return True
                except Exception:
                    pass
            
            return False
        except Exception:
            return False

    # 在最终判断之前，如果还不确定结果，尝试分析日志
    def on_operation_finished(self, success, message):
        # 检查 worker 中的注册状态进行更准确的判断
        if hasattr(self, 'registration_state'):
            # 如果 worker 报告注册成功，但传入的 success 为 False
            # 这可能是一个误报，我们相信 worker 的状态
            if self.registration_state == "success" and not success:
                success = True
        
        self.success_result = success
        self.operation_completed = True
        
        # 如果成功并且不是仅注册模式，保存账号
        if success and not self.register_only and hasattr(self.parent().parent(), 'save_current_account'):
            try:
                self.parent().parent().save_current_account()
                self.parent().parent().load_current_account()
            except Exception:
                pass
        
        # 在最终判断之前，如果还不确定结果，尝试分析日志
        if not self.operation_completed and not self.success_result:
            if self.analyze_logs_for_success_evidence():
                self.success_result = True

    def _check_auto_register_success_logs(self):
        """检查自动注册过程中可能生成的成功日志"""
        try:
            # 获取应用数据目录
            app_data_dir = get_app_data_dir()
            log_dir = os.path.join(app_data_dir, "logs")
            
            # 检查最常见的成功证据文件
            register_success_log = os.path.join(log_dir, "auto_register_success.log")
            if os.path.exists(register_success_log):
                return True
            
            # 其次，检查可能的一般日志文件
            auto_register_log = os.path.join(log_dir, "auto_register.log")
            if os.path.exists(auto_register_log):
                # 读取日志内容查找成功关键词
                try:
                    with open(auto_register_log, 'r', encoding='utf-8') as f:
                        log_content = f.read()
                        success_keywords = [
                            "注册成功", "成功创建账号", "验证码登录成功", 
                            "账号数据保存成功", "成功获取token", "accessToken获取成功"
                        ]
                        if any(keyword in log_content for keyword in success_keywords):
                            return True
                except Exception:
                    pass
            
            return False
        except Exception:
            return False