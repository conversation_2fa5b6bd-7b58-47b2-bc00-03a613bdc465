#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
认证时效检查定时器测试脚本
"""

import sys
import os
import time
from PySide6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QLabel, QPushButton
from PySide6.QtCore import QTimer

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from version_checker import VersionChecker

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("认证时效检查定时器测试")
        self.setGeometry(100, 100, 400, 300)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("状态: 未启动")
        layout.addWidget(self.status_label)
        
        # 间隔标签
        self.interval_label = QLabel("检查间隔: 未设置")
        layout.addWidget(self.interval_label)
        
        # 启动按钮
        self.start_button = QPushButton("启动认证时效检查定时器")
        self.start_button.clicked.connect(self.start_timer)
        layout.addWidget(self.start_button)
        
        # 停止按钮
        self.stop_button = QPushButton("停止认证时效检查定时器")
        self.stop_button.clicked.connect(self.stop_timer)
        self.stop_button.setEnabled(False)
        layout.addWidget(self.stop_button)
        
        # 测试按钮
        self.test_button = QPushButton("测试认证检查")
        self.test_button.clicked.connect(self.test_auth_check)
        layout.addWidget(self.test_button)
        
        # 创建VersionChecker实例
        self.version_checker = VersionChecker(self)
        
        # 创建状态更新定时器
        self.status_timer = QTimer()
        self.status_timer.timeout.connect(self.update_status)
        self.status_timer.start(1000)  # 每秒更新一次状态
        
    def start_timer(self):
        """启动认证时效检查定时器"""
        try:
            self.version_checker.start_auth_check_timer()
            self.status_label.setText("状态: 已启动")
            self.interval_label.setText(f"检查间隔: {self.version_checker.auth_check_interval_minutes}分钟")
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            print("认证时效检查定时器已启动")
        except Exception as e:
            self.status_label.setText(f"状态: 启动失败 - {e}")
            print(f"启动定时器失败: {e}")
    
    def stop_timer(self):
        """停止认证时效检查定时器"""
        try:
            self.version_checker.stop_auth_check_timer()
            self.status_label.setText("状态: 已停止")
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            print("认证时效检查定时器已停止")
        except Exception as e:
            self.status_label.setText(f"状态: 停止失败 - {e}")
            print(f"停止定时器失败: {e}")
    
    def test_auth_check(self):
        """测试认证检查功能"""
        try:
            print("开始测试认证检查...")
            self.version_checker._check_auth_expiry()
            print("认证检查测试完成")
        except Exception as e:
            print(f"认证检查测试失败: {e}")
    
    def update_status(self):
        """更新状态显示"""
        if hasattr(self.version_checker, 'auth_check_timer') and self.version_checker.auth_check_timer:
            if self.version_checker.auth_check_timer.isActive():
                remaining_ms = self.version_checker.auth_check_timer.remainingTime()
                remaining_sec = remaining_ms // 1000
                remaining_min = remaining_sec // 60
                remaining_sec = remaining_sec % 60
                self.status_label.setText(f"状态: 运行中 (下次检查: {remaining_min}:{remaining_sec:02d})")
            else:
                self.status_label.setText("状态: 定时器已创建但未激活")

def main():
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    window = TestWindow()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
